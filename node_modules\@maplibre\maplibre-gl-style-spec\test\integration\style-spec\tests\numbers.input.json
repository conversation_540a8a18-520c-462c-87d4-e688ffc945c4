{"version": 8, "sources": {"point": {"type": "g<PERSON><PERSON><PERSON>", "data": {"type": "FeatureCollection", "features": [{"type": "Feature", "geometry": {"type": "Point", "coordinates": [0, 0]}}]}}}, "layers": [{"id": "valid", "type": "circle", "source": "point", "paint": {"circle-radius": 5}}, {"id": "zero", "type": "circle", "source": "point", "paint": {"circle-radius": 0}}, {"id": "less-than-zero", "type": "circle", "source": "point", "paint": {"circle-radius": -1}}, {"id": "null-not-number", "type": "circle", "source": "point", "paint": {"circle-radius": null}}, {"id": "object-not-number", "type": "circle", "source": "point", "paint": {"circle-radius": {}}}, {"id": "array-not-number", "type": "circle", "source": "point", "paint": {"circle-radius": []}}, {"id": "boolean-not-number", "type": "circle", "source": "point", "paint": {"circle-radius": true}}, {"id": "expression", "type": "circle", "source": "point", "paint": {"circle-radius": ["sqrt", 16]}}, {"id": "expression-invalid", "type": "circle", "source": "point", "paint": {"circle-radius": ["/", 0, 0]}}]}