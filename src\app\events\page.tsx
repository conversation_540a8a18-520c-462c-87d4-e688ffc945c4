'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import EventCard from '@/components/events/EventCard'
import EventsFilter from '@/components/events/EventsFilter'
import { Calendar, MapPin, Filter } from 'lucide-react'

export default function EventsPage() {
  const [filters, setFilters] = useState({
    category: '',
    location: '',
    dateRange: 'all',
    priceRange: [0, 200],
    featured: false
  })

  const events = [
    {
      id: 1,
      title: 'Dubrovnik Summer Festival',
      category: 'Kultura',
      location: 'Dubrovnik',
      date: '2024-07-10',
      endDate: '2024-08-25',
      price: 50,
      image: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400&h=300&fit=crop',
      description: 'Međunarodni festival klasične glazbe, opere i teatra',
      featured: true,
      tags: ['Glazba', 'Teatar', 'Opera'],
      organizer: 'Dubrovnik Summer Festival',
      capacity: 500,
      attendees: 342
    },
    {
      id: 2,
      title: 'Pula Film Festival',
      category: 'Film',
      location: 'Pula',
      date: '2024-07-15',
      endDate: '2024-07-22',
      price: 25,
      image: 'https://images.unsplash.com/photo-1489599735734-79b4169c4388?w=400&h=300&fit=crop',
      description: 'Najstariji filmski festival u Hrvatskoj',
      featured: true,
      tags: ['Film', 'Kultura', 'Umjetnost'],
      organizer: 'Pula Film Festival',
      capacity: 1000,
      attendees: 756
    },
    {
      id: 3,
      title: 'Zagreb Wine Festival',
      category: 'Gastronomija',
      location: 'Zagreb',
      date: '2024-06-20',
      endDate: '2024-06-22',
      price: 35,
      image: 'https://images.unsplash.com/photo-1506377247377-2a5b3b417ebb?w=400&h=300&fit=crop',
      description: 'Degustacija najboljih hrvatskih vina',
      featured: false,
      tags: ['Vino', 'Gastronomija', 'Degustacija'],
      organizer: 'Zagreb Wine Association',
      capacity: 300,
      attendees: 189
    },
    {
      id: 4,
      title: 'Split Summer Festival',
      category: 'Glazba',
      location: 'Split',
      date: '2024-07-01',
      endDate: '2024-08-31',
      price: 40,
      image: 'https://images.unsplash.com/photo-1501386761578-eac5c94b800a?w=400&h=300&fit=crop',
      description: 'Ljetni festival glazbe u Dioklecijanovoj palači',
      featured: true,
      tags: ['Glazba', 'Koncert', 'Ljetni festival'],
      organizer: 'Split Cultural Center',
      capacity: 800,
      attendees: 623
    },
    {
      id: 5,
      title: 'Rovinj Photodays',
      category: 'Umjetnost',
      location: 'Rovinj',
      date: '2024-09-05',
      endDate: '2024-09-08',
      price: 20,
      image: 'https://images.unsplash.com/photo-1452587925148-ce544e77e70d?w=400&h=300&fit=crop',
      description: 'Međunarodni festival fotografije',
      featured: false,
      tags: ['Fotografija', 'Umjetnost', 'Radionica'],
      organizer: 'Rovinj Photo Club',
      capacity: 200,
      attendees: 87
    },
    {
      id: 6,
      title: 'Hvar Lavender Festival',
      category: 'Priroda',
      location: 'Hvar',
      date: '2024-06-15',
      endDate: '2024-06-16',
      price: 15,
      image: 'https://images.unsplash.com/photo-1499002238440-d264edd596ec?w=400&h=300&fit=crop',
      description: 'Proslava cvjetanja lavande na otoku Hvaru',
      featured: false,
      tags: ['Lavanda', 'Priroda', 'Tradicija'],
      organizer: 'Hvar Tourism Board',
      capacity: 150,
      attendees: 134
    },
    {
      id: 7,
      title: 'Zagreb Street Food Festival',
      category: 'Gastronomija',
      location: 'Zagreb',
      date: '2024-08-10',
      endDate: '2024-08-12',
      price: 0,
      image: 'https://images.unsplash.com/photo-1555939594-58d7cb561ad1?w=400&h=300&fit=crop',
      description: 'Besplatni festival street food kulture',
      featured: false,
      tags: ['Street food', 'Besplatno', 'Gastronomija'],
      organizer: 'Zagreb Events',
      capacity: 2000,
      attendees: 1456
    },
    {
      id: 8,
      title: 'Zadar Sea Organ Concert',
      category: 'Glazba',
      location: 'Zadar',
      date: '2024-07-20',
      endDate: '2024-07-20',
      price: 10,
      image: 'https://images.unsplash.com/photo-1571115764595-644a1f56a55c?w=400&h=300&fit=crop',
      description: 'Jedinstveni koncert uz morske orgulje',
      featured: true,
      tags: ['Morske orgulje', 'Glazba', 'Zalazak sunca'],
      organizer: 'Zadar Tourism',
      capacity: 100,
      attendees: 89
    }
  ]

  const [filteredEvents, setFilteredEvents] = useState(events)

  const handleFilterChange = (newFilters: any) => {
    setFilters(newFilters)
    
    let filtered = events.filter(event => {
      const matchesCategory = !newFilters.category || event.category === newFilters.category
      const matchesLocation = !newFilters.location || event.location === newFilters.location
      const matchesPrice = event.price >= newFilters.priceRange[0] && event.price <= newFilters.priceRange[1]
      const matchesFeatured = !newFilters.featured || event.featured
      
      // Date range filtering
      let matchesDate = true
      if (newFilters.dateRange !== 'all') {
        const eventDate = new Date(event.date)
        const now = new Date()
        
        switch (newFilters.dateRange) {
          case 'today':
            matchesDate = eventDate.toDateString() === now.toDateString()
            break
          case 'week':
            const weekFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)
            matchesDate = eventDate >= now && eventDate <= weekFromNow
            break
          case 'month':
            const monthFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000)
            matchesDate = eventDate >= now && eventDate <= monthFromNow
            break
        }
      }
      
      return matchesCategory && matchesLocation && matchesPrice && matchesFeatured && matchesDate
    })

    setFilteredEvents(filtered)
  }

  const categories = [...new Set(events.map(event => event.category))]
  const locations = [...new Set(events.map(event => event.location))]

  return (
    <div className="min-h-screen bg-gray-50 pt-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-2">
            Događaji u Hrvatskoj
          </h1>
          <p className="text-gray-600">
            Otkrijte kulturne događaje, festivale i skrivene dragulje
          </p>
        </motion.div>

        {/* Featured Events Banner */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="bg-gradient-to-r from-primary to-accent-600 rounded-2xl p-6 text-white">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold mb-2">Istaknuti događaji</h2>
                <p className="opacity-90">
                  Ne propustite najuzbudljivije događaje ovog ljeta
                </p>
              </div>
              <div className="text-right">
                <div className="text-3xl font-bold">
                  {events.filter(e => e.featured).length}
                </div>
                <div className="text-sm opacity-90">Istaknuto</div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Results Count */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6"
        >
          <p className="text-gray-600">
            Pronađeno {filteredEvents.length} događaja
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-4 gap-8">
          {/* Filters Sidebar */}
          <div className="lg:col-span-1">
            <EventsFilter
              filters={filters}
              categories={categories}
              locations={locations}
              onFilterChange={handleFilterChange}
            />
          </div>

          {/* Events Grid */}
          <div className="lg:col-span-3">
            {filteredEvents.length > 0 ? (
              <div className="grid md:grid-cols-2 gap-6">
                {filteredEvents.map((event, index) => (
                  <motion.div
                    key={event.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <EventCard event={event} />
                  </motion.div>
                ))}
              </div>
            ) : (
              /* No Results */
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="text-center py-12"
              >
                <Calendar className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  Nema događaja
                </h3>
                <p className="text-gray-600 mb-6">
                  Pokušajte promijeniti filtere za pronalaženje događaja
                </p>
                <button
                  onClick={() => handleFilterChange({
                    category: '',
                    location: '',
                    dateRange: 'all',
                    priceRange: [0, 200],
                    featured: false
                  })}
                  className="btn-primary"
                >
                  Resetiraj filtere
                </button>
              </motion.div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
