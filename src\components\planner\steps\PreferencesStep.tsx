'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Users, 
  Zap, 
  Coffee, 
  Gauge, 
  Sun, 
  Cloud, 
  Snowflake,
  Accessibility,
  Heart,
  UserCheck
} from 'lucide-react'

interface PreferencesStepProps {
  data: {
    travelPace: string
    tripType: string
    groupSize: number
    accessibility: boolean
    weather: string
  }
  onChange: (preferences: any) => void
}

const PreferencesStep = ({ data, onChange }: PreferencesStepProps) => {
  const [travelPace, setTravelPace] = useState(data?.travelPace || 'balanced')
  const [tripType, setTripType] = useState(data?.tripType || 'couple')
  const [groupSize, setGroupSize] = useState(data?.groupSize || 2)
  const [accessibility, setAccessibility] = useState(data?.accessibility || false)
  const [weather, setWeather] = useState(data?.weather || 'any')

  const travelPaces = [
    {
      id: 'relaxed',
      name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
      description: '<PERSON><PERSON><PERSON><PERSON> vremena za uživanje, manje aktiv<PERSON>',
      icon: Coffee,
      color: 'from-green-500 to-emerald-500'
    },
    {
      id: 'balanced',
      name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
      description: 'Savr<PERSON>ena kombinacija aktivnosti i odmora',
      icon: Gauge,
      color: 'from-blue-500 to-cyan-500'
    },
    {
      id: 'fast',
      name: 'Intenzivno',
      description: 'Maksimalno iskorištenje vremena',
      icon: Zap,
      color: 'from-orange-500 to-red-500'
    }
  ]

  const tripTypes = [
    {
      id: 'solo',
      name: 'Solo putovanje',
      description: 'Putovanje sam',
      icon: UserCheck,
      defaultSize: 1
    },
    {
      id: 'couple',
      name: 'Par',
      description: 'Romantično putovanje',
      icon: Heart,
      defaultSize: 2
    },
    {
      id: 'family',
      name: 'Obitelj',
      description: 'Obiteljsko putovanje',
      icon: Users,
      defaultSize: 4
    },
    {
      id: 'friends',
      name: 'Prijatelji',
      description: 'Putovanje s prijateljima',
      icon: Users,
      defaultSize: 6
    },
    {
      id: 'team',
      name: 'Team Building',
      description: 'Poslovno putovanje',
      icon: Users,
      defaultSize: 10
    }
  ]

  const weatherPreferences = [
    {
      id: 'any',
      name: 'Bilo koje',
      description: 'Nema preferencija',
      icon: Sun,
      color: 'from-gray-500 to-slate-500'
    },
    {
      id: 'sunny',
      name: 'Sunčano',
      description: 'Toplo i sunčano vrijeme',
      icon: Sun,
      color: 'from-yellow-500 to-orange-500'
    },
    {
      id: 'mild',
      name: 'Umjereno',
      description: 'Blago i ugodno vrijeme',
      icon: Cloud,
      color: 'from-blue-400 to-cyan-400'
    },
    {
      id: 'cool',
      name: 'Hladno',
      description: 'Hladnije vrijeme',
      icon: Snowflake,
      color: 'from-blue-600 to-indigo-600'
    }
  ]

  useEffect(() => {
    onChange({
      travelPace,
      tripType,
      groupSize,
      accessibility,
      weather
    })
  }, [travelPace, tripType, groupSize, accessibility, weather, onChange])

  const handleTripTypeChange = (type: string) => {
    setTripType(type)
    const selectedType = tripTypes.find(t => t.id === type)
    if (selectedType) {
      setGroupSize(selectedType.defaultSize)
    }
  }

  return (
    <div className="space-y-8">
      {/* Travel Pace */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">
          Tempo putovanja
        </h3>
        <div className="grid md:grid-cols-3 gap-4">
          {travelPaces.map((pace) => {
            const Icon = pace.icon
            return (
              <motion.button
                key={pace.id}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => setTravelPace(pace.id)}
                className={`p-4 rounded-xl border-2 transition-all duration-300 text-left ${
                  travelPace === pace.id
                    ? 'border-primary bg-primary/10'
                    : 'border-gray-200 hover:border-primary/50'
                }`}
              >
                <div className={`w-12 h-12 rounded-xl bg-gradient-to-br ${pace.color} flex items-center justify-center mb-3`}>
                  <Icon className="w-6 h-6 text-white" />
                </div>
                <div className={`font-semibold mb-1 ${
                  travelPace === pace.id ? 'text-primary' : 'text-gray-900'
                }`}>
                  {pace.name}
                </div>
                <div className={`text-sm ${
                  travelPace === pace.id ? 'text-primary/70' : 'text-gray-600'
                }`}>
                  {pace.description}
                </div>
              </motion.button>
            )
          })}
        </div>
      </div>

      {/* Trip Type */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">
          Tip putovanja
        </h3>
        <div className="grid md:grid-cols-3 lg:grid-cols-5 gap-4">
          {tripTypes.map((type) => {
            const Icon = type.icon
            return (
              <motion.button
                key={type.id}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => handleTripTypeChange(type.id)}
                className={`p-4 rounded-xl border-2 transition-all duration-300 text-center ${
                  tripType === type.id
                    ? 'border-primary bg-primary/10'
                    : 'border-gray-200 hover:border-primary/50'
                }`}
              >
                <Icon className={`w-8 h-8 mx-auto mb-2 ${
                  tripType === type.id ? 'text-primary' : 'text-gray-600'
                }`} />
                <div className={`font-medium mb-1 ${
                  tripType === type.id ? 'text-primary' : 'text-gray-900'
                }`}>
                  {type.name}
                </div>
                <div className={`text-xs ${
                  tripType === type.id ? 'text-primary/70' : 'text-gray-600'
                }`}>
                  {type.description}
                </div>
              </motion.button>
            )
          })}
        </div>
      </div>

      {/* Group Size */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">
          Veličina grupe
        </h3>
        <div className="flex items-center space-x-4">
          <Users className="w-5 h-5 text-primary" />
          <span className="font-medium text-gray-900">
            {groupSize} {groupSize === 1 ? 'osoba' : groupSize < 5 ? 'osobe' : 'osoba'}
          </span>
        </div>
        <input
          type="range"
          min="1"
          max="20"
          value={groupSize}
          onChange={(e) => setGroupSize(parseInt(e.target.value))}
          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
          style={{
            background: `linear-gradient(to right, #ad795a 0%, #ad795a ${(groupSize / 20) * 100}%, #e5e7eb ${(groupSize / 20) * 100}%, #e5e7eb 100%)`
          }}
        />
        <div className="flex justify-between text-sm text-gray-500">
          <span>1 osoba</span>
          <span>20 osoba</span>
        </div>
      </div>

      {/* Weather Preference */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">
          Preferencije vremena
        </h3>
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
          {weatherPreferences.map((weatherPref) => {
            const Icon = weatherPref.icon
            return (
              <motion.button
                key={weatherPref.id}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => setWeather(weatherPref.id)}
                className={`p-4 rounded-xl border-2 transition-all duration-300 text-center ${
                  weather === weatherPref.id
                    ? 'border-primary bg-primary/10'
                    : 'border-gray-200 hover:border-primary/50'
                }`}
              >
                <div className={`w-12 h-12 rounded-xl bg-gradient-to-br ${weatherPref.color} flex items-center justify-center mb-3 mx-auto`}>
                  <Icon className="w-6 h-6 text-white" />
                </div>
                <div className={`font-medium mb-1 ${
                  weather === weatherPref.id ? 'text-primary' : 'text-gray-900'
                }`}>
                  {weatherPref.name}
                </div>
                <div className={`text-sm ${
                  weather === weatherPref.id ? 'text-primary/70' : 'text-gray-600'
                }`}>
                  {weatherPref.description}
                </div>
              </motion.button>
            )
          })}
        </div>
      </div>

      {/* Accessibility */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">
          Dodatne opcije
        </h3>
        <motion.button
          whileHover={{ scale: 1.01 }}
          whileTap={{ scale: 0.99 }}
          onClick={() => setAccessibility(!accessibility)}
          className={`w-full p-4 rounded-xl border-2 transition-all duration-300 flex items-center space-x-4 ${
            accessibility
              ? 'border-primary bg-primary/10'
              : 'border-gray-200 hover:border-primary/50'
          }`}
        >
          <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${
            accessibility ? 'bg-primary text-white' : 'bg-gray-100 text-gray-600'
          }`}>
            <Accessibility className="w-6 h-6" />
          </div>
          <div className="flex-1 text-left">
            <div className={`font-semibold ${
              accessibility ? 'text-primary' : 'text-gray-900'
            }`}>
              Pristupačnost
            </div>
            <div className={`text-sm ${
              accessibility ? 'text-primary/70' : 'text-gray-600'
            }`}>
              Uključite opcije prilagođene osobama s invaliditetom
            </div>
          </div>
          <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
            accessibility 
              ? 'border-primary bg-primary' 
              : 'border-gray-300'
          }`}>
            {accessibility && (
              <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            )}
          </div>
        </motion.button>
      </div>

      {/* Summary */}
      <div className="bg-primary/10 p-6 rounded-xl">
        <h4 className="font-semibold text-primary mb-3">Sažetak vaših preferencija</h4>
        <div className="grid md:grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-600">Tempo: </span>
            <span className="font-medium text-gray-900">
              {travelPaces.find(p => p.id === travelPace)?.name}
            </span>
          </div>
          <div>
            <span className="text-gray-600">Tip: </span>
            <span className="font-medium text-gray-900">
              {tripTypes.find(t => t.id === tripType)?.name}
            </span>
          </div>
          <div>
            <span className="text-gray-600">Grupa: </span>
            <span className="font-medium text-gray-900">
              {groupSize} {groupSize === 1 ? 'osoba' : groupSize < 5 ? 'osobe' : 'osoba'}
            </span>
          </div>
          <div>
            <span className="text-gray-600">Vrijeme: </span>
            <span className="font-medium text-gray-900">
              {weatherPreferences.find(w => w.id === weather)?.name}
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default PreferencesStep
