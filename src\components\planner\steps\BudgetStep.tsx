'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Euro, DollarSign, CreditCard, PiggyBank } from 'lucide-react'

interface BudgetStepProps {
  data: {
    min: number
    max: number
    currency: string
  }
  onChange: (budget: any) => void
}

const BudgetStep = ({ data, onChange }: BudgetStepProps) => {
  const [minBudget, setMinBudget] = useState(data?.min || 100)
  const [maxBudget, setMaxBudget] = useState(data?.max || 1000)
  const [currency, setCurrency] = useState(data?.currency || 'EUR')

  const currencies = [
    { code: 'EUR', symbol: '€', name: 'Euro', icon: Euro },
    { code: 'USD', symbol: '$', name: 'US Dollar', icon: DollarSign },
    { code: 'HRK', symbol: 'kn', name: 'Hrvatska kuna', icon: CreditCard }
  ]

  const budgetRanges = [
    { label: 'Ekonomično', min: 50, max: 200, icon: PiggyBank, color: 'from-green-500 to-emerald-500' },
    { label: 'Umjereno', min: 200, max: 500, icon: CreditCard, color: 'from-blue-500 to-cyan-500' },
    { label: 'Komforno', min: 500, max: 1000, icon: Euro, color: 'from-purple-500 to-indigo-500' },
    { label: 'Luksuzno', min: 1000, max: 3000, icon: DollarSign, color: 'from-yellow-500 to-orange-500' }
  ]

  useEffect(() => {
    onChange({
      min: minBudget,
      max: maxBudget,
      currency
    })
  }, [minBudget, maxBudget, currency, onChange])

  const setBudgetRange = (min: number, max: number) => {
    setMinBudget(min)
    setMaxBudget(max)
  }

  const formatCurrency = (amount: number) => {
    const selectedCurrency = currencies.find(c => c.code === currency)
    return `${selectedCurrency?.symbol}${amount}`
  }

  return (
    <div className="space-y-8">
      {/* Currency Selection */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">
          Odaberite valutu
        </h3>
        <div className="grid grid-cols-3 gap-4">
          {currencies.map((curr) => {
            const Icon = curr.icon
            return (
              <motion.button
                key={curr.code}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => setCurrency(curr.code)}
                className={`p-4 rounded-xl border-2 transition-all duration-300 ${
                  currency === curr.code
                    ? 'border-primary bg-primary/10 text-primary'
                    : 'border-gray-200 hover:border-primary/50 text-gray-700'
                }`}
              >
                <Icon className="w-6 h-6 mx-auto mb-2" />
                <div className="font-medium">{curr.code}</div>
                <div className="text-sm opacity-70">{curr.name}</div>
              </motion.button>
            )
          })}
        </div>
      </div>

      {/* Budget Range Presets */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">
          Brzi odabir budžeta
        </h3>
        <div className="grid md:grid-cols-2 gap-4">
          {budgetRanges.map((range) => {
            const Icon = range.icon
            const isSelected = minBudget === range.min && maxBudget === range.max
            
            return (
              <motion.button
                key={range.label}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => setBudgetRange(range.min, range.max)}
                className={`p-4 rounded-xl border-2 transition-all duration-300 text-left ${
                  isSelected
                    ? 'border-primary bg-primary/10'
                    : 'border-gray-200 hover:border-primary/50'
                }`}
              >
                <div className="flex items-center space-x-3">
                  <div className={`w-12 h-12 rounded-xl bg-gradient-to-br ${range.color} flex items-center justify-center`}>
                    <Icon className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <div className={`font-semibold ${isSelected ? 'text-primary' : 'text-gray-900'}`}>
                      {range.label}
                    </div>
                    <div className={`text-sm ${isSelected ? 'text-primary/70' : 'text-gray-600'}`}>
                      {formatCurrency(range.min)} - {formatCurrency(range.max)}
                    </div>
                  </div>
                </div>
              </motion.button>
            )
          })}
        </div>
      </div>

      {/* Custom Budget Range */}
      <div className="space-y-6">
        <h3 className="text-lg font-semibold text-gray-900">
          Prilagodite svoj budžet
        </h3>

        {/* Current Range Display */}
        <div className="text-center p-6 bg-primary/10 rounded-xl">
          <div className="text-2xl font-bold text-primary mb-2">
            {formatCurrency(minBudget)} - {formatCurrency(maxBudget)}
          </div>
          <div className="text-gray-600">
            Vaš budžet po osobi
          </div>
        </div>

        {/* Min Budget Slider */}
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <label className="font-medium text-gray-700">Minimalni budžet</label>
            <span className="text-primary font-semibold">{formatCurrency(minBudget)}</span>
          </div>
          <input
            type="range"
            min="50"
            max="2000"
            step="50"
            value={minBudget}
            onChange={(e) => {
              const value = parseInt(e.target.value)
              setMinBudget(value)
              if (value >= maxBudget) {
                setMaxBudget(value + 100)
              }
            }}
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            style={{
              background: `linear-gradient(to right, #ad795a 0%, #ad795a ${(minBudget / 2000) * 100}%, #e5e7eb ${(minBudget / 2000) * 100}%, #e5e7eb 100%)`
            }}
          />
        </div>

        {/* Max Budget Slider */}
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <label className="font-medium text-gray-700">Maksimalni budžet</label>
            <span className="text-primary font-semibold">{formatCurrency(maxBudget)}</span>
          </div>
          <input
            type="range"
            min="100"
            max="3000"
            step="50"
            value={maxBudget}
            onChange={(e) => {
              const value = parseInt(e.target.value)
              setMaxBudget(value)
              if (value <= minBudget) {
                setMinBudget(value - 100)
              }
            }}
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            style={{
              background: `linear-gradient(to right, #ad795a 0%, #ad795a ${(maxBudget / 3000) * 100}%, #e5e7eb ${(maxBudget / 3000) * 100}%, #e5e7eb 100%)`
            }}
          />
        </div>
      </div>

      {/* Budget Breakdown */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">
          Što uključuje vaš budžet?
        </h3>
        <div className="grid md:grid-cols-2 gap-4">
          {[
            { category: 'Smještaj', percentage: 40, amount: Math.round((minBudget + maxBudget) / 2 * 0.4) },
            { category: 'Hrana', percentage: 30, amount: Math.round((minBudget + maxBudget) / 2 * 0.3) },
            { category: 'Aktivnosti', percentage: 20, amount: Math.round((minBudget + maxBudget) / 2 * 0.2) },
            { category: 'Transport', percentage: 10, amount: Math.round((minBudget + maxBudget) / 2 * 0.1) }
          ].map((item) => (
            <div key={item.category} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
              <div>
                <div className="font-medium text-gray-900">{item.category}</div>
                <div className="text-sm text-gray-600">{item.percentage}%</div>
              </div>
              <div className="text-primary font-semibold">
                {formatCurrency(item.amount)}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Tips */}
      <div className="bg-blue-50 p-4 rounded-xl">
        <h4 className="font-medium text-blue-900 mb-2">💡 Savjeti za budžet</h4>
        <ul className="text-blue-800 text-sm space-y-1">
          <li>• Uključite 10-20% rezerve za neočekivane troškove</li>
          <li>• Viši budžet omogućava više opcija i fleksibilnosti</li>
          <li>• AI će optimizirati troškove prema vašim prioritetima</li>
          <li>• Grupna putovanja često imaju bolju vrijednost za novac</li>
        </ul>
      </div>
    </div>
  )
}

export default BudgetStep
