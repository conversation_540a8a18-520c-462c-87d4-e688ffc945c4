import { NextRequest, NextResponse } from 'next/server'
import { openRouterClient, ChatMessage } from '@/lib/ai/openrouter'

export async function POST(request: NextRequest) {
  try {
    const { messages, context } = await request.json()
    
    // Validate messages
    if (!messages || !Array.isArray(messages)) {
      return NextResponse.json(
        { error: 'Poruke su obavezne' },
        { status: 400 }
      )
    }

    // Get response from OpenRouter
    const response = await openRouterClient.chatWithBot(messages, context)
    
    return NextResponse.json({
      success: true,
      response
    })
  } catch (error) {
    console.error('Error in chat:', error)
    
    return NextResponse.json(
      { 
        error: '<PERSON><PERSON><PERSON><PERSON> u komunikaciji s botom',
        details: error instanceof Error ? error.message : 'Nepoznata greška'
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Eventria AI Chat Bot',
    status: 'active',
    version: '1.0.0'
  })
}
