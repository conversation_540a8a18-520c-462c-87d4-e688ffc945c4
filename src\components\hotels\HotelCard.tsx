'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'
import { 
  Star, 
  MapPin, 
  Euro, 
  ExternalLink, 
  Heart,
  Wifi,
  Car,
  Waves,
  UtensilsCrossed,
  Coffee
} from 'lucide-react'
import { useState } from 'react'

interface HotelCardProps {
  hotel: {
    id: number
    name: string
    location: string
    rating: number
    price: number
    image: string
    amenities: string[]
    roomType: string
    distance: number
    description: string
    highlights: string[]
  }
}

const HotelCard = ({ hotel }: HotelCardProps) => {
  const [isFavorite, setIsFavorite] = useState(false)
  const [imageLoaded, setImageLoaded] = useState(false)

  const getAmenityIcon = (amenity: string) => {
    switch (amenity.toLowerCase()) {
      case 'wifi': return Wifi
      case 'parking': return Car
      case 'bazen': return Waves
      case 'restoran': return UtensilsCrossed
      case 'spa': return Coffee
      default: return Star
    }
  }

  return (
    <motion.div
      whileHover={{ y: -5 }}
      className="card overflow-hidden group cursor-pointer"
    >
      {/* Image */}
      <div className="relative h-48 overflow-hidden">
        <Image
          src={hotel.image}
          alt={hotel.name}
          fill
          className={`object-cover group-hover:scale-110 transition-transform duration-500 ${
            imageLoaded ? 'opacity-100' : 'opacity-0'
          }`}
          onLoad={() => setImageLoaded(true)}
        />
        
        {/* Loading placeholder */}
        {!imageLoaded && (
          <div className="absolute inset-0 bg-gray-200 animate-pulse" />
        )}

        {/* Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

        {/* Favorite Button */}
        <motion.button
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={(e) => {
            e.stopPropagation()
            setIsFavorite(!isFavorite)
          }}
          className="absolute top-3 right-3 w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-sm"
        >
          <Heart 
            className={`w-4 h-4 transition-colors ${
              isFavorite ? 'text-red-500 fill-current' : 'text-gray-600'
            }`} 
          />
        </motion.button>

        {/* Price Badge */}
        <div className="absolute top-3 left-3 bg-primary text-white px-3 py-1 rounded-lg font-semibold">
          €{hotel.price}
        </div>

        {/* Distance Badge */}
        <div className="absolute bottom-3 left-3 bg-white/90 backdrop-blur-sm text-gray-900 px-2 py-1 rounded-lg text-xs font-medium">
          {hotel.distance} km od centra
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <h3 className="text-lg font-bold text-gray-900 mb-1 group-hover:text-primary transition-colors">
              {hotel.name}
            </h3>
            <div className="flex items-center space-x-1 text-gray-600 text-sm">
              <MapPin className="w-4 h-4" />
              <span>{hotel.location}</span>
            </div>
          </div>
          
          {/* Rating */}
          <div className="flex items-center space-x-1 bg-yellow-50 px-2 py-1 rounded-lg">
            <Star className="w-4 h-4 text-yellow-500 fill-current" />
            <span className="text-sm font-semibold text-gray-900">{hotel.rating}</span>
          </div>
        </div>

        {/* Description */}
        <p className="text-gray-600 text-sm mb-4 line-clamp-2">
          {hotel.description}
        </p>

        {/* Room Type */}
        <div className="mb-4">
          <span className="bg-secondary text-primary px-3 py-1 rounded-full text-sm font-medium">
            {hotel.roomType}
          </span>
        </div>

        {/* Amenities */}
        <div className="flex flex-wrap gap-2 mb-4">
          {hotel.amenities.slice(0, 4).map((amenity, index) => {
            const Icon = getAmenityIcon(amenity)
            return (
              <div
                key={index}
                className="flex items-center space-x-1 bg-gray-100 px-2 py-1 rounded-lg"
              >
                <Icon className="w-3 h-3 text-gray-600" />
                <span className="text-xs text-gray-600">{amenity}</span>
              </div>
            )
          })}
          {hotel.amenities.length > 4 && (
            <div className="bg-gray-100 px-2 py-1 rounded-lg">
              <span className="text-xs text-gray-600">
                +{hotel.amenities.length - 4} više
              </span>
            </div>
          )}
        </div>

        {/* Highlights */}
        <div className="space-y-1 mb-4">
          {hotel.highlights.slice(0, 2).map((highlight, index) => (
            <div key={index} className="flex items-center space-x-2 text-sm text-gray-600">
              <div className="w-1.5 h-1.5 bg-primary rounded-full" />
              <span>{highlight}</span>
            </div>
          ))}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-100">
          <div className="text-sm text-gray-600">
            <span>Od </span>
            <span className="text-xl font-bold text-primary">€{hotel.price}</span>
            <span> / noć</span>
          </div>
          
          <div className="flex items-center space-x-2">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="text-primary hover:text-accent-700 transition-colors"
            >
              <ExternalLink className="w-4 h-4" />
            </motion.button>
            
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-primary text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-accent-700 transition-colors"
            >
              Rezerviraj
            </motion.button>
          </div>
        </div>
      </div>
    </motion.div>
  )
}

export default HotelCard
