const OPENROUTER_API_KEY = '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'
const OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1'

export interface ChatMessage {
  role: 'system' | 'user' | 'assistant'
  content: string
}

export interface ItineraryRequest {
  destinations: string[]
  dates: {
    startDate?: string
    endDate?: string
    flexible: boolean
    duration?: number
  }
  interests: string[]
  budget: {
    min: number
    max: number
    currency: string
  }
  preferences: {
    travelPace: string
    tripType: string
    groupSize: number
    accessibility: boolean
    weather: string
  }
}

export class OpenRouterClient {
  private apiKey: string
  private baseUrl: string

  constructor() {
    this.apiKey = OPENROUTER_API_KEY
    this.baseUrl = OPENROUTER_BASE_URL
  }

  async generateItinerary(request: ItineraryRequest): Promise<any> {
    const systemPrompt = `You are <PERSON><PERSON><PERSON><PERSON>, an expert Croatian travel planner AI. Create detailed, personalized travel itineraries for Croatia. 

Key guidelines:
- Focus on Croatian destinations, culture, and experiences
- Include specific locations, activities, restaurants, and accommodations
- Provide realistic timing and costs in EUR
- Consider Croatian weather, seasons, and local events
- Include hidden gems and local recommendations
- Optimize routes and logistics
- Respect the user's budget and preferences
- Include cultural insights and local tips

Response format should be a detailed JSON itinerary with:
- Daily schedules with specific times
- Activity descriptions and locations
- Cost estimates
- Transportation details
- Restaurant and accommodation recommendations
- Local tips and cultural insights

Always respond in Croatian language.`

    const userPrompt = this.buildUserPrompt(request)

    const messages: ChatMessage[] = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: userPrompt }
    ]

    try {
      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://eventria.hr',
          'X-Title': 'Eventria AI Travel Planner'
        },
        body: JSON.stringify({
          model: 'anthropic/claude-3.5-sonnet', // Using Claude for better Croatian language support
          messages,
          temperature: 0.7,
          max_tokens: 4000
        })
      })

      if (!response.ok) {
        throw new Error(`OpenRouter API error: ${response.status}`)
      }

      const data = await response.json()
      return this.parseItineraryResponse(data.choices[0].message.content)
    } catch (error) {
      console.error('Error generating itinerary:', error)
      // Return fallback itinerary
      return this.getFallbackItinerary(request)
    }
  }

  async chatWithBot(messages: ChatMessage[], context?: any): Promise<string> {
    const systemPrompt = `You are EventriaBot, a helpful Croatian travel assistant. You help users modify and improve their travel itineraries.

Guidelines:
- Always respond in Croatian
- Be helpful and enthusiastic about Croatian travel
- Provide specific, actionable suggestions
- Consider local knowledge and insider tips
- Help with itinerary modifications, additions, and optimizations
- Suggest alternatives when needed
- Be concise but informative`

    const chatMessages: ChatMessage[] = [
      { role: 'system', content: systemPrompt },
      ...messages
    ]

    try {
      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://eventria.hr',
          'X-Title': 'Eventria AI Travel Planner'
        },
        body: JSON.stringify({
          model: 'anthropic/claude-3.5-sonnet',
          messages: chatMessages,
          temperature: 0.8,
          max_tokens: 1000
        })
      })

      if (!response.ok) {
        throw new Error(`OpenRouter API error: ${response.status}`)
      }

      const data = await response.json()
      return data.choices[0].message.content
    } catch (error) {
      console.error('Error in chat:', error)
      return 'Izvините, trenutno ne mogu odgovoriti. Molimo pokušajte ponovno.'
    }
  }

  private buildUserPrompt(request: ItineraryRequest): string {
    const destinations = request.destinations.join(', ')
    const interests = request.interests.join(', ')
    const budget = `${request.budget.min}-${request.budget.max} ${request.budget.currency}`
    
    let dateInfo = ''
    if (request.dates.flexible) {
      dateInfo = `Fleksibilni datumi, trajanje: ${request.dates.duration} dana`
    } else {
      dateInfo = `${request.dates.startDate} do ${request.dates.endDate}`
    }

    return `Kreiraj detaljni itinerar za putovanje po Hrvatskoj:

Destinacije: ${destinations}
Datumi: ${dateInfo}
Interesi: ${interests}
Budžet: ${budget} po osobi
Grupa: ${request.preferences.groupSize} ${request.preferences.groupSize === 1 ? 'osoba' : 'osoba'}
Tip putovanja: ${request.preferences.tripType}
Tempo: ${request.preferences.travelPace}
Vremenske preferencije: ${request.preferences.weather}
${request.preferences.accessibility ? 'Potrebna pristupačnost' : ''}

Molim kreiraj detaljni itinerar s aktivnostima, restoranima, smještajem i lokalnim preporukama. Uključi specifične lokacije, vremena, cijene i praktične savjete.`
  }

  private parseItineraryResponse(content: string): any {
    try {
      // Try to extract JSON from the response
      const jsonMatch = content.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0])
      }
    } catch (error) {
      console.error('Error parsing JSON response:', error)
    }

    // If JSON parsing fails, create structured response from text
    return this.parseTextToItinerary(content)
  }

  private parseTextToItinerary(content: string): any {
    // Basic text parsing to create itinerary structure
    const lines = content.split('\n').filter(line => line.trim())
    
    return {
      title: 'AI Generirani Itinerar',
      description: 'Personalizirani itinerar kreiran od strane EventriaBot',
      duration: 3,
      totalBudget: 500,
      currency: 'EUR',
      days: [
        {
          day: 1,
          date: new Date().toISOString().split('T')[0],
          title: 'Dan 1 - Dolazak i istraživanje',
          activities: [
            {
              time: '09:00',
              title: 'Dolazak na destinaciju',
              description: 'Početak vašeg putovanja',
              location: 'Glavna destinacija',
              duration: '2h',
              cost: 50,
              type: 'transport'
            }
          ]
        }
      ],
      recommendations: {
        restaurants: [],
        accommodations: [],
        tips: content.split('\n').slice(0, 5)
      }
    }
  }

  private getFallbackItinerary(request: ItineraryRequest): any {
    const destination = request.destinations[0] || 'Zagreb'
    
    return {
      title: `Putovanje u ${destination}`,
      description: 'Osnovni itinerar kreiran kada AI nije dostupan',
      duration: request.dates.duration || 3,
      totalBudget: request.budget.max,
      currency: request.budget.currency,
      days: [
        {
          day: 1,
          date: request.dates.startDate || new Date().toISOString().split('T')[0],
          title: 'Dan 1 - Dolazak i upoznavanje',
          activities: [
            {
              time: '10:00',
              title: `Dolazak u ${destination}`,
              description: 'Početak vašeg putovanja',
              location: destination,
              duration: '2h',
              cost: 50,
              type: 'transport'
            },
            {
              time: '14:00',
              title: 'Obilazak centra grada',
              description: 'Šetnja kroz glavni dio grada',
              location: `Centar ${destination}`,
              duration: '3h',
              cost: 0,
              type: 'sightseeing'
            }
          ]
        }
      ],
      recommendations: {
        restaurants: [`Lokalni restoran u ${destination}`],
        accommodations: [`Hotel u ${destination}`],
        tips: [
          'Ponesite udobnu obuću za hodanje',
          'Provjerite radno vrijeme muzeja',
          'Pokušajte lokalnu kuhinju'
        ]
      }
    }
  }
}

export const openRouterClient = new OpenRouterClient()
