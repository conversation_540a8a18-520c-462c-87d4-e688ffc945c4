'use client'

import { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  X, 
  Send, 
  Bot, 
  User, 
  Sparkles,
  Plus,
  Minus,
  MapPin,
  Clock
} from 'lucide-react'

interface EventriaBotProps {
  itinerary: any
  onClose: () => void
  onUpdateItinerary: (itinerary: any) => void
}

const EventriaBot = ({ itinerary, onClose, onUpdateItinerary }: EventriaBotProps) => {
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: 'bot',
      content: 'Pozdrav! Ja sam EventriaBot 🤖 Mogu vam pomoći prilagoditi vaš itinerar. Što biste htjeli promijeniti?',
      timestamp: new Date()
    }
  ])
  const [inputValue, setInputValue] = useState('')
  const [isTyping, setIsTyping] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const quickActions = [
    { id: 'add-day', label: 'Dodaj dan', icon: Plus },
    { id: 'remove-day', label: 'Ukloni dan', icon: Minus },
    { id: 'add-activity', label: 'Dodaj aktivnost', icon: MapPin },
    { id: 'change-budget', label: 'Promijeni budžet', icon: Sparkles }
  ]

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  const handleSendMessage = async (content: string) => {
    if (!content.trim()) return

    // Add user message
    const userMessage = {
      id: Date.now(),
      type: 'user',
      content,
      timestamp: new Date()
    }
    setMessages(prev => [...prev, userMessage])
    setInputValue('')
    setIsTyping(true)

    // Simulate AI response
    setTimeout(() => {
      const botResponse = generateBotResponse(content)
      setMessages(prev => [...prev, botResponse])
      setIsTyping(false)
    }, 1500)
  }

  const generateBotResponse = (userInput: string) => {
    const input = userInput.toLowerCase()
    let response = ''

    if (input.includes('dodaj dan') || input.includes('add day')) {
      response = 'Odličo! Dodao sam još jedan dan u vaš itinerar. Novi dan uključuje dodatne aktivnosti koje odgovaraju vašim interesima. Želite li da specificiram što biste htjeli raditi tog dana?'
      // Here you would actually modify the itinerary
    } else if (input.includes('ukloni') || input.includes('remove')) {
      response = 'Razumijem da želite skratiti putovanje. Koji dan biste htjeli ukloniti? Mogu preporučiti dan s najmanje aktivnosti ili možete specificirati.'
    } else if (input.includes('budžet') || input.includes('budget')) {
      response = 'Mogu prilagoditi itinerar vašem novom budžetu. Kakav je vaš novi budžet? Mogu pronaći jeftinije opcije ili dodati luksuznije aktivnosti.'
    } else if (input.includes('hrana') || input.includes('food') || input.includes('restoran')) {
      response = 'Odličan izbor! Mogu dodati više gastronomskih iskustava. Preferirate li tradicionalnu hrvatsku kuhinju, međunarodne restorane ili možda street food?'
    } else if (input.includes('aktivnost') || input.includes('activity')) {
      response = 'Mogu dodati nove aktivnosti u vaš itinerar. Što vas zanima? Priroda, kultura, avantura, wellness ili nešto drugo?'
    } else {
      response = 'Razumijem vaš zahtjev. Analiziram najbolje opcije za vas... Mogu prilagoditi itinerar prema vašim željama. Možete biti specifičniji o tome što želite promijeniti?'
    }

    return {
      id: Date.now(),
      type: 'bot',
      content: response,
      timestamp: new Date()
    }
  }

  const handleQuickAction = (actionId: string) => {
    const actions = {
      'add-day': 'Dodaj još jedan dan u itinerar',
      'remove-day': 'Ukloni zadnji dan iz itinerara',
      'add-activity': 'Dodaj novu aktivnost u postojeći dan',
      'change-budget': 'Promijeni budžet putovanja'
    }
    
    handleSendMessage(actions[actionId as keyof typeof actions])
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="bg-white rounded-2xl shadow-2xl w-full max-w-2xl h-[600px] flex flex-col"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-primary to-accent-600 rounded-xl flex items-center justify-center">
              <Bot className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">EventriaBot</h3>
              <p className="text-sm text-gray-600">AI asistent za putovanja</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-6 space-y-4">
          {messages.map((message) => (
            <motion.div
              key={message.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div className={`flex items-start space-x-3 max-w-[80%] ${
                message.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''
              }`}>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                  message.type === 'user' 
                    ? 'bg-primary text-white' 
                    : 'bg-gray-100 text-gray-600'
                }`}>
                  {message.type === 'user' ? (
                    <User className="w-4 h-4" />
                  ) : (
                    <Bot className="w-4 h-4" />
                  )}
                </div>
                <div className={`rounded-2xl px-4 py-3 ${
                  message.type === 'user'
                    ? 'bg-primary text-white'
                    : 'bg-gray-100 text-gray-900'
                }`}>
                  <p className="text-sm">{message.content}</p>
                  <p className={`text-xs mt-1 ${
                    message.type === 'user' ? 'text-white/70' : 'text-gray-500'
                  }`}>
                    {message.timestamp.toLocaleTimeString('hr-HR', { 
                      hour: '2-digit', 
                      minute: '2-digit' 
                    })}
                  </p>
                </div>
              </div>
            </motion.div>
          ))}

          {/* Typing Indicator */}
          {isTyping && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex justify-start"
            >
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 rounded-full bg-gray-100 text-gray-600 flex items-center justify-center">
                  <Bot className="w-4 h-4" />
                </div>
                <div className="bg-gray-100 rounded-2xl px-4 py-3">
                  <div className="flex space-x-1">
                    <motion.div
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 1, repeat: Infinity, delay: 0 }}
                      className="w-2 h-2 bg-gray-400 rounded-full"
                    />
                    <motion.div
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 1, repeat: Infinity, delay: 0.2 }}
                      className="w-2 h-2 bg-gray-400 rounded-full"
                    />
                    <motion.div
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 1, repeat: Infinity, delay: 0.4 }}
                      className="w-2 h-2 bg-gray-400 rounded-full"
                    />
                  </div>
                </div>
              </div>
            </motion.div>
          )}
          <div ref={messagesEndRef} />
        </div>

        {/* Quick Actions */}
        <div className="px-6 py-4 border-t border-gray-100">
          <div className="flex flex-wrap gap-2 mb-4">
            {quickActions.map((action) => {
              const Icon = action.icon
              return (
                <motion.button
                  key={action.id}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => handleQuickAction(action.id)}
                  className="flex items-center space-x-2 bg-gray-100 hover:bg-primary hover:text-white text-gray-700 px-3 py-2 rounded-lg text-sm transition-all duration-300"
                >
                  <Icon className="w-4 h-4" />
                  <span>{action.label}</span>
                </motion.button>
              )
            })}
          </div>

          {/* Input */}
          <div className="flex space-x-3">
            <input
              type="text"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSendMessage(inputValue)}
              placeholder="Opišite što želite promijeniti..."
              className="flex-1 px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300"
            />
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => handleSendMessage(inputValue)}
              disabled={!inputValue.trim() || isTyping}
              className="bg-primary text-white p-3 rounded-xl hover:bg-accent-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <Send className="w-5 h-5" />
            </motion.button>
          </div>
        </div>
      </motion.div>
    </motion.div>
  )
}

export default EventriaBot
