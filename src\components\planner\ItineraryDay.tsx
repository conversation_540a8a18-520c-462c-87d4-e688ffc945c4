'use client'

import { motion } from 'framer-motion'
import { 
  Clock, 
  MapPin, 
  Euro, 
  Car, 
  Utensils, 
  Camera, 
  TreePine,
  Building,
  ExternalLink,
  Edit3
} from 'lucide-react'

interface ItineraryDayProps {
  day: {
    day: number
    date: string
    title: string
    activities: Array<{
      time: string
      title: string
      description: string
      location: string
      duration: string
      cost: number
      type: string
    }>
  }
}

const ItineraryDay = ({ day }: ItineraryDayProps) => {
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'transport': return Car
      case 'food': return Utensils
      case 'sightseeing': return Camera
      case 'nature': return TreePine
      case 'culture': return Building
      default: return MapPin
    }
  }

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'transport': return 'from-blue-500 to-cyan-500'
      case 'food': return 'from-orange-500 to-red-500'
      case 'sightseeing': return 'from-purple-500 to-pink-500'
      case 'nature': return 'from-green-500 to-emerald-500'
      case 'culture': return 'from-indigo-500 to-blue-500'
      default: return 'from-gray-500 to-slate-500'
    }
  }

  const totalCost = day.activities.reduce((sum, activity) => sum + activity.cost, 0)

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="card p-6"
    >
      {/* Day Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-1">
            Dan {day.day}: {day.title}
          </h2>
          <p className="text-gray-600">
            {new Date(day.date).toLocaleDateString('hr-HR', {
              weekday: 'long',
              day: 'numeric',
              month: 'long'
            })}
          </p>
        </div>
        <div className="text-right">
          <div className="text-2xl font-bold text-primary">€{totalCost}</div>
          <div className="text-sm text-gray-600">Ukupno za dan</div>
        </div>
      </div>

      {/* Timeline */}
      <div className="relative">
        {/* Timeline Line */}
        <div className="absolute left-6 top-0 bottom-0 w-0.5 bg-gray-200"></div>

        {/* Activities */}
        <div className="space-y-6">
          {day.activities.map((activity, index) => {
            const Icon = getActivityIcon(activity.type)
            const colorClass = getActivityColor(activity.type)
            
            return (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="relative flex items-start space-x-4"
              >
                {/* Timeline Dot */}
                <div className={`relative z-10 w-12 h-12 rounded-xl bg-gradient-to-br ${colorClass} flex items-center justify-center shadow-lg`}>
                  <Icon className="w-6 h-6 text-white" />
                </div>

                {/* Activity Content */}
                <div className="flex-1 bg-white border border-gray-100 rounded-xl p-4 shadow-sm hover:shadow-md transition-shadow">
                  <div className="flex items-start justify-between mb-3">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-1">
                        {activity.title}
                      </h3>
                      <p className="text-gray-600 mb-2">
                        {activity.description}
                      </p>
                    </div>
                    <button className="text-gray-400 hover:text-primary transition-colors">
                      <Edit3 className="w-4 h-4" />
                    </button>
                  </div>

                  {/* Activity Meta */}
                  <div className="grid md:grid-cols-4 gap-4 text-sm">
                    <div className="flex items-center space-x-2 text-gray-600">
                      <Clock className="w-4 h-4" />
                      <span>{activity.time}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-gray-600">
                      <MapPin className="w-4 h-4" />
                      <span>{activity.location}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-gray-600">
                      <Clock className="w-4 h-4" />
                      <span>{activity.duration}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-primary font-medium">
                      <Euro className="w-4 h-4" />
                      <span>€{activity.cost}</span>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex items-center space-x-3 mt-4 pt-4 border-t border-gray-100">
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="flex items-center space-x-2 text-primary hover:text-accent-700 transition-colors"
                    >
                      <MapPin className="w-4 h-4" />
                      <span>Vidi na mapi</span>
                    </motion.button>
                    
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="flex items-center space-x-2 text-gray-600 hover:text-primary transition-colors"
                    >
                      <ExternalLink className="w-4 h-4" />
                      <span>Više info</span>
                    </motion.button>
                  </div>
                </div>
              </motion.div>
            )
          })}
        </div>
      </div>

      {/* Day Summary */}
      <div className="mt-8 p-4 bg-gray-50 rounded-xl">
        <div className="grid md:grid-cols-3 gap-4 text-sm">
          <div className="text-center">
            <div className="font-semibold text-gray-900">
              {day.activities.length} aktivnosti
            </div>
            <div className="text-gray-600">Planirano za dan</div>
          </div>
          <div className="text-center">
            <div className="font-semibold text-gray-900">
              {day.activities.reduce((sum, activity) => {
                const duration = parseFloat(activity.duration.replace('h', ''))
                return sum + duration
              }, 0)}h
            </div>
            <div className="text-gray-600">Ukupno vrijeme</div>
          </div>
          <div className="text-center">
            <div className="font-semibold text-primary">€{totalCost}</div>
            <div className="text-gray-600">Troškovi dana</div>
          </div>
        </div>
      </div>
    </motion.div>
  )
}

export default ItineraryDay
