'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  MapPin, 
  Euro, 
  Star, 
  Wifi, 
  Car, 
  Coffee,
  Waves,
  Dumbbell,
  UtensilsCrossed,
  Bed,
  Filter,
  X
} from 'lucide-react'

interface HotelsFilterProps {
  filters: any
  onFilterChange: (filters: any) => void
}

const HotelsFilter = ({ filters, onFilterChange }: HotelsFilterProps) => {
  const [isOpen, setIsOpen] = useState(false)

  const amenitiesList = [
    { id: 'WiFi', name: 'WiFi', icon: Wifi },
    { id: 'Parking', name: 'Parking', icon: Car },
    { id: 'Bazen', name: '<PERSON>zen', icon: Waves },
    { id: 'Fitness', name: 'Fitness', icon: Dumbbell },
    { id: 'Restoran', name: '<PERSON>oran', icon: UtensilsCrossed },
    { id: 'Spa', name: 'Spa', icon: Coffee },
  ]

  const roomTypes = [
    'Standard soba',
    'Superior soba',
    'Deluxe soba',
    'Suite',
    'Apartman',
    'Villa'
  ]

  const locations = [
    'Zagreb',
    'Split',
    'Dubrovnik',
    'Rovinj',
    '<PERSON><PERSON>',
    '<PERSON>adar',
    '<PERSON><PERSON>',
    'Ri<PERSON><PERSON>'
  ]

  const handleFilterUpdate = (key: string, value: any) => {
    const newFilters = { ...filters, [key]: value }
    onFilterChange(newFilters)
  }

  const handleAmenityToggle = (amenity: string) => {
    const currentAmenities = filters.amenities || []
    const newAmenities = currentAmenities.includes(amenity)
      ? currentAmenities.filter((a: string) => a !== amenity)
      : [...currentAmenities, amenity]
    
    handleFilterUpdate('amenities', newAmenities)
  }

  const clearFilters = () => {
    onFilterChange({
      location: '',
      priceRange: [50, 500],
      rating: 0,
      amenities: [],
      roomType: '',
      distance: 10
    })
  }

  return (
    <>
      {/* Mobile Filter Button */}
      <div className="lg:hidden mb-6">
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={() => setIsOpen(true)}
          className="w-full flex items-center justify-center space-x-2 bg-white border border-gray-200 rounded-xl py-3 px-4 shadow-sm"
        >
          <Filter className="w-5 h-5 text-gray-600" />
          <span className="font-medium text-gray-900">Filtri</span>
        </motion.button>
      </div>

      {/* Desktop Filters */}
      <div className="hidden lg:block">
        <FilterContent
          filters={filters}
          amenitiesList={amenitiesList}
          roomTypes={roomTypes}
          locations={locations}
          onFilterUpdate={handleFilterUpdate}
          onAmenityToggle={handleAmenityToggle}
          onClearFilters={clearFilters}
        />
      </div>

      {/* Mobile Filter Modal */}
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="lg:hidden fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-end"
        >
          <motion.div
            initial={{ y: '100%' }}
            animate={{ y: 0 }}
            exit={{ y: '100%' }}
            className="bg-white rounded-t-2xl w-full max-h-[80vh] overflow-y-auto"
          >
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Filtri</h3>
              <button
                onClick={() => setIsOpen(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
            <div className="p-6">
              <FilterContent
                filters={filters}
                amenitiesList={amenitiesList}
                roomTypes={roomTypes}
                locations={locations}
                onFilterUpdate={handleFilterUpdate}
                onAmenityToggle={handleAmenityToggle}
                onClearFilters={clearFilters}
              />
              <div className="mt-6 pt-6 border-t border-gray-200">
                <button
                  onClick={() => setIsOpen(false)}
                  className="w-full btn-primary"
                >
                  Primijeni filtere
                </button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </>
  )
}

const FilterContent = ({ 
  filters, 
  amenitiesList, 
  roomTypes, 
  locations,
  onFilterUpdate, 
  onAmenityToggle, 
  onClearFilters 
}: any) => {
  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">Filtri</h3>
        <button
          onClick={onClearFilters}
          className="text-primary hover:text-accent-700 text-sm font-medium transition-colors"
        >
          Očisti sve
        </button>
      </div>

      {/* Location */}
      <div className="space-y-3">
        <label className="block text-sm font-medium text-gray-700">
          Lokacija
        </label>
        <div className="relative">
          <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <select
            value={filters.location}
            onChange={(e) => onFilterUpdate('location', e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
          >
            <option value="">Sve lokacije</option>
            {locations.map((location: string) => (
              <option key={location} value={location}>
                {location}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Price Range */}
      <div className="space-y-3">
        <label className="block text-sm font-medium text-gray-700">
          Cijena po noći
        </label>
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>€{filters.priceRange[0]}</span>
            <span>€{filters.priceRange[1]}</span>
          </div>
          <input
            type="range"
            min="50"
            max="500"
            step="10"
            value={filters.priceRange[1]}
            onChange={(e) => onFilterUpdate('priceRange', [filters.priceRange[0], parseInt(e.target.value)])}
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            style={{
              background: `linear-gradient(to right, #ad795a 0%, #ad795a ${(filters.priceRange[1] / 500) * 100}%, #e5e7eb ${(filters.priceRange[1] / 500) * 100}%, #e5e7eb 100%)`
            }}
          />
        </div>
      </div>

      {/* Rating */}
      <div className="space-y-3">
        <label className="block text-sm font-medium text-gray-700">
          Minimalna ocjena
        </label>
        <div className="flex space-x-2">
          {[1, 2, 3, 4, 5].map((rating) => (
            <motion.button
              key={rating}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => onFilterUpdate('rating', rating === filters.rating ? 0 : rating)}
              className={`p-2 rounded-lg transition-colors ${
                rating <= filters.rating
                  ? 'text-yellow-500'
                  : 'text-gray-300 hover:text-yellow-400'
              }`}
            >
              <Star className="w-5 h-5 fill-current" />
            </motion.button>
          ))}
        </div>
      </div>

      {/* Room Type */}
      <div className="space-y-3">
        <label className="block text-sm font-medium text-gray-700">
          Tip sobe
        </label>
        <div className="relative">
          <Bed className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <select
            value={filters.roomType}
            onChange={(e) => onFilterUpdate('roomType', e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
          >
            <option value="">Svi tipovi</option>
            {roomTypes.map((type: string) => (
              <option key={type} value={type}>
                {type}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Distance */}
      <div className="space-y-3">
        <label className="block text-sm font-medium text-gray-700">
          Udaljenost od centra (km)
        </label>
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>0 km</span>
            <span>{filters.distance} km</span>
          </div>
          <input
            type="range"
            min="0"
            max="20"
            step="0.5"
            value={filters.distance}
            onChange={(e) => onFilterUpdate('distance', parseFloat(e.target.value))}
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            style={{
              background: `linear-gradient(to right, #ad795a 0%, #ad795a ${(filters.distance / 20) * 100}%, #e5e7eb ${(filters.distance / 20) * 100}%, #e5e7eb 100%)`
            }}
          />
        </div>
      </div>

      {/* Amenities */}
      <div className="space-y-3">
        <label className="block text-sm font-medium text-gray-700">
          Sadržaji
        </label>
        <div className="grid grid-cols-2 gap-3">
          {amenitiesList.map((amenity: any) => {
            const Icon = amenity.icon
            const isSelected = filters.amenities?.includes(amenity.id)
            
            return (
              <motion.button
                key={amenity.id}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => onAmenityToggle(amenity.id)}
                className={`flex items-center space-x-2 p-3 rounded-lg border transition-all duration-300 ${
                  isSelected
                    ? 'border-primary bg-primary/10 text-primary'
                    : 'border-gray-200 hover:border-primary/50 text-gray-700'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span className="text-sm font-medium">{amenity.name}</span>
              </motion.button>
            )
          })}
        </div>
      </div>
    </div>
  )
}

export default HotelsFilter
