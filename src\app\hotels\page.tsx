'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import HotelsFilter from '@/components/hotels/HotelsFilter'
import HotelCard from '@/components/hotels/HotelCard'
import HotelMap from '@/components/hotels/HotelMap'
import { MapPin, Grid, Map } from 'lucide-react'

export default function HotelsPage() {
  const [viewMode, setViewMode] = useState<'grid' | 'map'>('grid')
  const [filters, setFilters] = useState({
    location: '',
    priceRange: [50, 500],
    rating: 0,
    amenities: [],
    roomType: '',
    distance: 10
  })

  const hotels = [
    {
      id: 1,
      name: 'Hotel Dubrovnik Palace',
      location: 'Dubrovnik',
      rating: 4.8,
      price: 299,
      image: 'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=400&h=300&fit=crop',
      amenities: ['WiFi', 'Bazen', 'Spa', 'Restoran', 'Parking'],
      roomType: 'Deluxe soba',
      distance: 0.5,
      description: 'Luksuzni hotel s pogledom na Jadransko more',
      highlights: ['Najbolji za romantične getaway', 'Blizu Starog grada']
    },
    {
      id: 2,
      name: 'Villa Dalmacija',
      location: 'Split',
      rating: 4.6,
      price: 189,
      image: 'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=400&h=300&fit=crop',
      amenities: ['WiFi', 'Klima', 'Terasa', 'Kuhinja'],
      roomType: 'Apartman',
      distance: 1.2,
      description: 'Moderna villa u srcu Splita',
      highlights: ['Odličo za obitelji', 'Blizu Dioklecijanove palače']
    },
    {
      id: 3,
      name: 'Hotel Esplanade Zagreb',
      location: 'Zagreb',
      rating: 4.9,
      price: 249,
      image: 'https://images.unsplash.com/photo-1578774204375-826dc5d996ed?w=400&h=300&fit=crop',
      amenities: ['WiFi', 'Fitness', 'Restoran', 'Bar', 'Concierge'],
      roomType: 'Superior soba',
      distance: 0.3,
      description: 'Povijesni hotel u centru Zagreba',
      highlights: ['Najbolji za poslovne putnike', 'Blizu glavnog kolodvora']
    },
    {
      id: 4,
      name: 'Hotel Lone Rovinj',
      location: 'Rovinj',
      rating: 4.7,
      price: 329,
      image: 'https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=400&h=300&fit=crop',
      amenities: ['WiFi', 'Bazen', 'Spa', 'Plaža', 'Restoran'],
      roomType: 'Sea view soba',
      distance: 2.1,
      description: 'Dizajnerski hotel s pogledom na more',
      highlights: ['Savršeno za wellness', 'Privatna plaža']
    },
    {
      id: 5,
      name: 'Heritage Hotel Forza',
      location: 'Baska Voda',
      rating: 4.5,
      price: 159,
      image: 'https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?w=400&h=300&fit=crop',
      amenities: ['WiFi', 'Bazen', 'Restoran', 'Terasa'],
      roomType: 'Standard soba',
      distance: 0.8,
      description: 'Boutique hotel na Makarskoj rivijeri',
      highlights: ['Odličo za parove', 'Tradicionalna arhitektura']
    },
    {
      id: 6,
      name: 'Hotel Park Hvar',
      location: 'Hvar',
      rating: 4.4,
      price: 279,
      image: 'https://images.unsplash.com/photo-1520250497591-112f2f40a3f4?w=400&h=300&fit=crop',
      amenities: ['WiFi', 'Bazen', 'Spa', 'Restoran', 'Marina'],
      roomType: 'Premium soba',
      distance: 1.5,
      description: 'Luksuzni resort na otoku Hvaru',
      highlights: ['Najbolji za ljetovanje', 'Blizu lavandnih polja']
    }
  ]

  const [filteredHotels, setFilteredHotels] = useState(hotels)

  const handleFilterChange = (newFilters: any) => {
    setFilters(newFilters)
    
    // Apply filters
    let filtered = hotels.filter(hotel => {
      const matchesLocation = !newFilters.location || 
        hotel.location.toLowerCase().includes(newFilters.location.toLowerCase())
      const matchesPrice = hotel.price >= newFilters.priceRange[0] && 
        hotel.price <= newFilters.priceRange[1]
      const matchesRating = hotel.rating >= newFilters.rating
      const matchesDistance = hotel.distance <= newFilters.distance
      const matchesRoomType = !newFilters.roomType || 
        hotel.roomType.toLowerCase().includes(newFilters.roomType.toLowerCase())
      const matchesAmenities = newFilters.amenities.length === 0 ||
        newFilters.amenities.every((amenity: string) => hotel.amenities.includes(amenity))

      return matchesLocation && matchesPrice && matchesRating && 
             matchesDistance && matchesRoomType && matchesAmenities
    })

    setFilteredHotels(filtered)
  }

  return (
    <div className="min-h-screen bg-gray-50 pt-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-2">
                Hoteli u Hrvatskoj
              </h1>
              <p className="text-gray-600">
                Pronađite savršen smještaj za vaše putovanje
              </p>
            </div>

            {/* View Toggle */}
            <div className="flex items-center space-x-2 bg-white rounded-xl p-1 shadow-sm">
              <button
                onClick={() => setViewMode('grid')}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-all duration-300 ${
                  viewMode === 'grid'
                    ? 'bg-primary text-white shadow-sm'
                    : 'text-gray-600 hover:text-primary'
                }`}
              >
                <Grid className="w-4 h-4" />
                <span>Lista</span>
              </button>
              <button
                onClick={() => setViewMode('map')}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-all duration-300 ${
                  viewMode === 'map'
                    ? 'bg-primary text-white shadow-sm'
                    : 'text-gray-600 hover:text-primary'
                }`}
              >
                <Map className="w-4 h-4" />
                <span>Mapa</span>
              </button>
            </div>
          </div>
        </motion.div>

        {/* Results Count */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6"
        >
          <p className="text-gray-600">
            Pronađeno {filteredHotels.length} hotela
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-4 gap-8">
          {/* Filters Sidebar */}
          <div className="lg:col-span-1">
            <HotelsFilter
              filters={filters}
              onFilterChange={handleFilterChange}
            />
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {viewMode === 'grid' ? (
              /* Hotels Grid */
              <div className="grid md:grid-cols-2 gap-6">
                {filteredHotels.map((hotel, index) => (
                  <motion.div
                    key={hotel.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <HotelCard hotel={hotel} />
                  </motion.div>
                ))}
              </div>
            ) : (
              /* Hotels Map */
              <HotelMap hotels={filteredHotels} />
            )}

            {/* No Results */}
            {filteredHotels.length === 0 && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="text-center py-12"
              >
                <MapPin className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  Nema rezultata
                </h3>
                <p className="text-gray-600 mb-6">
                  Pokušajte promijeniti filtere za pronalaženje hotela
                </p>
                <button
                  onClick={() => handleFilterChange({
                    location: '',
                    priceRange: [50, 500],
                    rating: 0,
                    amenities: [],
                    roomType: '',
                    distance: 10
                  })}
                  className="btn-primary"
                >
                  Resetiraj filtere
                </button>
              </motion.div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
