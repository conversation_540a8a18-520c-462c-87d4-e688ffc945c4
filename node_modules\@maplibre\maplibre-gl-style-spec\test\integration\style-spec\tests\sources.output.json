[{"message": "sources.missing-type: \"type\" is required", "line": 4}, {"message": "sources.invalid-type.type: expected one of [vector, raster, raster-dem, geojson, video, image], \"invalid\" found", "line": 7}, {"message": "sources.video-missing-coordinates: missing required property \"coordinates\"", "line": 26}, {"message": "sources.video-wrong-coordinates.coordinates[0]: array expected, number found", "line": 34}, {"message": "sources.video-wrong-coordinates.coordinates[1]: array expected, string found", "line": 34}, {"message": "sources.video-wrong-coordinates.coordinates[2][1]: number expected, string found", "line": 34}, {"message": "sources.video-wrong-coordinates.coordinates[3]: array length 2 expected, length 0 found", "line": 34}, {"message": "sources.canvas: Please use runtime APIs to add canvas sources, rather than including them in stylesheets.", "identifier": "source.canvas"}, {"message": "sources.cluster-properties.zoom.map: \"zoom\" and \"feature-state\" expressions are not supported with cluster properties.", "line": 49}, {"message": "sources.cluster-properties.state.map: \"zoom\" and \"feature-state\" expressions are not supported with cluster properties.", "line": 50}]