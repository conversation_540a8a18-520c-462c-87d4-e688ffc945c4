'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Calendar, 
  MapPin, 
  Users, 
  Star,
  Plus,
  Edit3,
  Share2,
  Download,
  Clock,
  Euro,
  Bookmark,
  Heart,
  Settings
} from 'lucide-react'
import TripCard from '@/components/dashboard/TripCard'
import StatsCard from '@/components/dashboard/StatsCard'
import RecentActivity from '@/components/dashboard/RecentActivity'

export default function DashboardPage() {
  const [activeTab, setActiveTab] = useState('trips')

  const userStats = {
    totalTrips: 12,
    savedEvents: 28,
    totalSpent: 3450,
    favoriteDestinations: ['Dubrovnik', 'Split', 'Rovinj']
  }

  const savedTrips = [
    {
      id: 1,
      title: 'Romantični vikend u Dubrovniku',
      destination: 'Dubrovnik',
      dates: '2024-07-15 - 2024-07-17',
      status: 'planned',
      budget: 650,
      image: 'https://images.unsplash.com/photo-1555990538-c3d4d7d1e8e5?w=400&h=300&fit=crop',
      activities: 8,
      days: 3,
      travelers: 2,
      lastModified: '2024-06-10'
    },
    {
      id: 2,
      title: 'Team Building Plitvička jezera',
      destination: 'Plitvička jezera',
      dates: '2024-08-20 - 2024-08-22',
      status: 'draft',
      budget: 1200,
      image: 'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=400&h=300&fit=crop',
      activities: 12,
      days: 3,
      travelers: 15,
      lastModified: '2024-06-08'
    },
    {
      id: 3,
      title: 'Istraživanje Istre',
      destination: 'Rovinj, Pula, Motovun',
      dates: '2024-09-10 - 2024-09-15',
      status: 'completed',
      budget: 890,
      image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop',
      activities: 18,
      days: 6,
      travelers: 4,
      lastModified: '2024-05-20'
    }
  ]

  const savedEvents = [
    {
      id: 1,
      title: 'Dubrovnik Summer Festival',
      location: 'Dubrovnik',
      date: '2024-07-20',
      price: 50,
      category: 'Kultura',
      saved: true
    },
    {
      id: 2,
      title: 'Zagreb Wine Festival',
      location: 'Zagreb',
      date: '2024-06-25',
      price: 35,
      category: 'Gastronomija',
      saved: true
    },
    {
      id: 3,
      title: 'Split Summer Festival',
      location: 'Split',
      date: '2024-08-01',
      price: 40,
      category: 'Glazba',
      saved: true
    }
  ]

  const recentActivity = [
    {
      id: 1,
      type: 'trip_created',
      title: 'Kreiran novi itinerar',
      description: 'Romantični vikend u Dubrovniku',
      timestamp: '2024-06-10T14:30:00Z',
      icon: Plus
    },
    {
      id: 2,
      type: 'event_saved',
      title: 'Događaj spremljen',
      description: 'Dubrovnik Summer Festival',
      timestamp: '2024-06-09T16:45:00Z',
      icon: Bookmark
    },
    {
      id: 3,
      type: 'trip_shared',
      title: 'Itinerar podijeljen',
      description: 'Team Building Plitvička jezera',
      timestamp: '2024-06-08T11:20:00Z',
      icon: Share2
    }
  ]

  const tabs = [
    { id: 'trips', name: 'Moja putovanja', icon: MapPin },
    { id: 'events', name: 'Spremljeni događaji', icon: Calendar },
    { id: 'favorites', name: 'Favoriti', icon: Heart },
    { id: 'settings', name: 'Postavke', icon: Settings }
  ]

  return (
    <div className="min-h-screen bg-gray-50 pt-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-2">
                Moj Dashboard
              </h1>
              <p className="text-gray-600">
                Upravljajte svojim putovanjima i događajima
              </p>
            </div>
            
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="btn-primary flex items-center space-x-2"
            >
              <Plus className="w-5 h-5" />
              <span>Novo putovanje</span>
            </motion.button>
          </div>
        </motion.div>

        {/* Stats Cards */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <StatsCard
            title="Ukupno putovanja"
            value={userStats.totalTrips}
            icon={MapPin}
            color="from-blue-500 to-cyan-500"
            change="+2 ovaj mjesec"
          />
          <StatsCard
            title="Spremljeni događaji"
            value={userStats.savedEvents}
            icon={Calendar}
            color="from-green-500 to-emerald-500"
            change="+5 ovaj tjedan"
          />
          <StatsCard
            title="Ukupno potrošeno"
            value={`€${userStats.totalSpent}`}
            icon={Euro}
            color="from-purple-500 to-indigo-500"
            change="Prosjek €287/putovanje"
          />
          <StatsCard
            title="Prosječna ocjena"
            value="4.8"
            icon={Star}
            color="from-yellow-500 to-orange-500"
            change="Odličan putnik!"
          />
        </div>

        {/* Tabs */}
        <div className="mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 overflow-x-auto">
              {tabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <motion.button
                    key={tab.id}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors ${
                      activeTab === tab.id
                        ? 'border-primary text-primary'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    <span>{tab.name}</span>
                  </motion.button>
                )
              })}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {activeTab === 'trips' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-6"
              >
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-bold text-gray-900">
                    Moja putovanja ({savedTrips.length})
                  </h2>
                  <div className="flex items-center space-x-2">
                    <select className="border border-gray-200 rounded-lg px-3 py-2 text-sm">
                      <option>Sva putovanja</option>
                      <option>Planirana</option>
                      <option>Završena</option>
                      <option>Nacrti</option>
                    </select>
                  </div>
                </div>
                
                <div className="space-y-4">
                  {savedTrips.map((trip, index) => (
                    <motion.div
                      key={trip.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <TripCard trip={trip} />
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            )}

            {activeTab === 'events' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-6"
              >
                <h2 className="text-xl font-bold text-gray-900">
                  Spremljeni događaji ({savedEvents.length})
                </h2>
                
                <div className="grid gap-4">
                  {savedEvents.map((event, index) => (
                    <motion.div
                      key={event.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="card p-4 flex items-center justify-between"
                    >
                      <div className="flex items-center space-x-4">
                        <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center">
                          <Calendar className="w-6 h-6 text-primary" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900">{event.title}</h3>
                          <div className="flex items-center space-x-4 text-sm text-gray-600">
                            <span>{event.location}</span>
                            <span>{new Date(event.date).toLocaleDateString('hr-HR')}</span>
                            <span className="bg-secondary text-primary px-2 py-1 rounded-full text-xs">
                              {event.category}
                            </span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-3">
                        <span className="font-semibold text-primary">
                          {event.price === 0 ? 'Besplatno' : `€${event.price}`}
                        </span>
                        <button className="text-gray-400 hover:text-red-500 transition-colors">
                          <Heart className="w-5 h-5 fill-current" />
                        </button>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            )}

            {activeTab === 'favorites' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-6"
              >
                <h2 className="text-xl font-bold text-gray-900">
                  Omiljene destinacije
                </h2>
                
                <div className="grid md:grid-cols-2 gap-4">
                  {userStats.favoriteDestinations.map((destination, index) => (
                    <motion.div
                      key={destination}
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: index * 0.1 }}
                      className="card p-6 text-center"
                    >
                      <div className="w-16 h-16 bg-gradient-to-br from-primary to-accent-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <MapPin className="w-8 h-8 text-white" />
                      </div>
                      <h3 className="font-bold text-gray-900 mb-2">{destination}</h3>
                      <p className="text-gray-600 text-sm">
                        {Math.floor(Math.random() * 5) + 1} putovanja
                      </p>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            )}

            {activeTab === 'settings' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-6"
              >
                <h2 className="text-xl font-bold text-gray-900">
                  Postavke računa
                </h2>
                
                <div className="card p-6 space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Ime i prezime
                    </label>
                    <input
                      type="text"
                      defaultValue="Marko Marković"
                      className="w-full px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Email
                    </label>
                    <input
                      type="email"
                      defaultValue="<EMAIL>"
                      className="w-full px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Preferencije obavještenja
                    </label>
                    <div className="space-y-3">
                      {[
                        'Email obavještenja o novim događajima',
                        'SMS podsjetnici za putovanja',
                        'Preporuke na temelju interesa'
                      ].map((option, index) => (
                        <label key={index} className="flex items-center space-x-3">
                          <input
                            type="checkbox"
                            defaultChecked={index < 2}
                            className="rounded border-gray-300 text-primary focus:ring-primary"
                          />
                          <span className="text-gray-700">{option}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                  
                  <button className="btn-primary">
                    Spremi promjene
                  </button>
                </div>
              </motion.div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            <RecentActivity activities={recentActivity} />
            
            {/* Quick Actions */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="card p-6"
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Brze akcije
              </h3>
              <div className="space-y-3">
                {[
                  { icon: Plus, label: 'Kreiraj novo putovanje', color: 'text-blue-600' },
                  { icon: Calendar, label: 'Pretraži događaje', color: 'text-green-600' },
                  { icon: Share2, label: 'Podijeli itinerar', color: 'text-purple-600' },
                  { icon: Download, label: 'Izvezi PDF', color: 'text-orange-600' }
                ].map((action, index) => {
                  const Icon = action.icon
                  return (
                    <motion.button
                      key={index}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      className="w-full flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors text-left"
                    >
                      <Icon className={`w-5 h-5 ${action.color}`} />
                      <span className="font-medium text-gray-900">{action.label}</span>
                    </motion.button>
                  )
                })}
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  )
}
