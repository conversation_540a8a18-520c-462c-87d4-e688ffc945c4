'use client'

import { motion } from 'framer-motion'
import { 
  MapPin, 
  Calendar, 
  Heart, 
  Euro, 
  Users, 
  Gauge,
  Sun,
  Edit3,
  CheckCircle
} from 'lucide-react'

interface ReviewStepProps {
  formData: any
  onChange: (data: any) => void
}

const ReviewStep = ({ formData }: ReviewStepProps) => {
  const formatCurrency = (amount: number, currency: string) => {
    const symbols = { EUR: '€', USD: '$', HRK: 'kn' }
    return `${symbols[currency as keyof typeof symbols] || '€'}${amount}`
  }

  const formatDate = (dateString: string) => {
    if (!dateString) return 'Nije odabrano'
    return new Date(dateString).toLocaleDateString('hr-HR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    })
  }

  const getTravelPaceName = (pace: string) => {
    const paces = {
      relaxed: 'Opušteno',
      balanced: 'Uravnoteženo',
      fast: 'Intenzivno'
    }
    return paces[pace as keyof typeof paces] || pace
  }

  const getTripTypeName = (type: string) => {
    const types = {
      solo: 'Solo putovanje',
      couple: 'Par',
      family: '<PERSON><PERSON>el<PERSON>',
      friends: '<PERSON><PERSON><PERSON>telji',
      team: 'Team Building'
    }
    return types[type as keyof typeof types] || type
  }

  const getWeatherName = (weather: string) => {
    const weathers = {
      any: 'Bilo koje',
      sunny: 'Sunčano',
      mild: 'Umjereno',
      cool: 'Hladno'
    }
    return weathers[weather as keyof typeof weathers] || weather
  }

  const reviewSections = [
    {
      title: 'Destinacije',
      icon: MapPin,
      color: 'from-blue-500 to-cyan-500',
      content: (
        <div className="space-y-2">
          {formData.destinations?.length > 0 ? (
            formData.destinations.map((dest: string, index: number) => (
              <div key={index} className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <span>{dest}</span>
              </div>
            ))
          ) : (
            <span className="text-gray-500">Nema odabranih destinacija</span>
          )}
        </div>
      )
    },
    {
      title: 'Datumi',
      icon: Calendar,
      color: 'from-green-500 to-emerald-500',
      content: (
        <div className="space-y-2">
          {formData.dates?.flexible ? (
            <div>
              <div className="font-medium">Fleksibilni datumi</div>
              <div className="text-gray-600">
                Trajanje: {formData.dates.duration} {formData.dates.duration === 1 ? 'dan' : 'dana'}
              </div>
            </div>
          ) : (
            <div>
              <div>Polazak: {formatDate(formData.dates?.startDate)}</div>
              <div>Povratak: {formatDate(formData.dates?.endDate)}</div>
              {formData.dates?.startDate && formData.dates?.endDate && (
                <div className="text-gray-600">
                  Trajanje: {Math.ceil((new Date(formData.dates.endDate).getTime() - new Date(formData.dates.startDate).getTime()) / (1000 * 60 * 60 * 24))} dana
                </div>
              )}
            </div>
          )}
        </div>
      )
    },
    {
      title: 'Interesi',
      icon: Heart,
      color: 'from-pink-500 to-rose-500',
      content: (
        <div className="flex flex-wrap gap-2">
          {formData.interests?.length > 0 ? (
            formData.interests.map((interest: string, index: number) => (
              <span
                key={index}
                className="bg-primary/10 text-primary px-3 py-1 rounded-full text-sm font-medium"
              >
                {interest}
              </span>
            ))
          ) : (
            <span className="text-gray-500">Nema odabranih interesa</span>
          )}
        </div>
      )
    },
    {
      title: 'Budžet',
      icon: Euro,
      color: 'from-yellow-500 to-orange-500',
      content: (
        <div className="space-y-2">
          <div className="text-2xl font-bold text-primary">
            {formatCurrency(formData.budget?.min || 0, formData.budget?.currency || 'EUR')} - {formatCurrency(formData.budget?.max || 0, formData.budget?.currency || 'EUR')}
          </div>
          <div className="text-gray-600">Po osobi</div>
        </div>
      )
    },
    {
      title: 'Preferencije',
      icon: Users,
      color: 'from-purple-500 to-indigo-500',
      content: (
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Gauge className="w-4 h-4 text-gray-600" />
            <span>Tempo: {getTravelPaceName(formData.preferences?.travelPace)}</span>
          </div>
          <div className="flex items-center space-x-2">
            <Users className="w-4 h-4 text-gray-600" />
            <span>Tip: {getTripTypeName(formData.preferences?.tripType)}</span>
          </div>
          <div className="flex items-center space-x-2">
            <Users className="w-4 h-4 text-gray-600" />
            <span>Grupa: {formData.preferences?.groupSize} {formData.preferences?.groupSize === 1 ? 'osoba' : 'osoba'}</span>
          </div>
          <div className="flex items-center space-x-2">
            <Sun className="w-4 h-4 text-gray-600" />
            <span>Vrijeme: {getWeatherName(formData.preferences?.weather)}</span>
          </div>
          {formData.preferences?.accessibility && (
            <div className="flex items-center space-x-2 text-primary">
              <CheckCircle className="w-4 h-4" />
              <span>Pristupačnost uključena</span>
            </div>
          )}
        </div>
      )
    }
  ]

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <p className="text-gray-600">
          Provjerite sve svoje odabire prije kreiranja itinerara
        </p>
      </div>

      {/* Review Sections */}
      <div className="space-y-6">
        {reviewSections.map((section, index) => {
          const Icon = section.icon
          return (
            <motion.div
              key={section.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="card p-6"
            >
              <div className="flex items-start space-x-4">
                <div className={`w-12 h-12 rounded-xl bg-gradient-to-br ${section.color} flex items-center justify-center flex-shrink-0`}>
                  <Icon className="w-6 h-6 text-white" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-lg font-semibold text-gray-900">
                      {section.title}
                    </h3>
                    <button className="text-primary hover:text-accent-700 transition-colors">
                      <Edit3 className="w-4 h-4" />
                    </button>
                  </div>
                  {section.content}
                </div>
              </div>
            </motion.div>
          )
        })}
      </div>

      {/* Summary */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
        className="bg-gradient-to-br from-primary/10 to-secondary/20 p-6 rounded-xl"
      >
        <h3 className="text-xl font-bold text-gray-900 mb-4">
          🎯 Vaš AI će kreirati
        </h3>
        <div className="grid md:grid-cols-2 gap-4 text-sm">
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-4 h-4 text-green-600" />
              <span>Personalizirani itinerar dan po dan</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-4 h-4 text-green-600" />
              <span>Preporuke za smještaj i restorane</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-4 h-4 text-green-600" />
              <span>Interaktivne mape s lokacijama</span>
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-4 h-4 text-green-600" />
              <span>Optimizirane rute i transport</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-4 h-4 text-green-600" />
              <span>Budžet breakdown i savjeti</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-4 h-4 text-green-600" />
              <span>Mogućnost uređivanja s EventriaBot</span>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Final CTA */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
        className="text-center p-6 bg-white border-2 border-dashed border-primary/30 rounded-xl"
      >
        <div className="text-primary text-4xl mb-4">🚀</div>
        <h3 className="text-xl font-bold text-gray-900 mb-2">
          Sve je spremno!
        </h3>
        <p className="text-gray-600">
          Kliknite "Kreiraj itinerar" i naš AI će u nekoliko sekundi kreirati vaše savršeno putovanje
        </p>
      </motion.div>
    </div>
  )
}

export default ReviewStep
