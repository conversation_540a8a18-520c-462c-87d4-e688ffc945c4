'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Filter, 
  X, 
  Calendar, 
  MapPin, 
  Euro, 
  Star,
  Music,
  Palette,
  UtensilsCrossed,
  Camera,
  TreePine
} from 'lucide-react'

interface EventsFilterProps {
  filters: any
  categories: string[]
  locations: string[]
  onFilterChange: (filters: any) => void
}

const EventsFilter = ({ filters, categories, locations, onFilterChange }: EventsFilterProps) => {
  const [isOpen, setIsOpen] = useState(false)

  const dateRanges = [
    { id: 'all', name: '<PERSON><PERSON> da<PERSON><PERSON>' },
    { id: 'today', name: '<PERSON><PERSON>' },
    { id: 'week', name: '<PERSON><PERSON><PERSON> tjedan' },
    { id: 'month', name: '<PERSON><PERSON>j mjesec' }
  ]

  const getCategoryIcon = (category: string) => {
    switch (category.toLowerCase()) {
      case 'glazba': return Music
      case 'kultura': return Palette
      case 'gastronomija': return UtensilsCrossed
      case 'film': return Camera
      case 'priroda': return TreePine
      case 'umjetnost': return Palette
      default: return Star
    }
  }

  const handleFilterUpdate = (key: string, value: any) => {
    const newFilters = { ...filters, [key]: value }
    onFilterChange(newFilters)
  }

  const clearFilters = () => {
    onFilterChange({
      category: '',
      location: '',
      dateRange: 'all',
      priceRange: [0, 200],
      featured: false
    })
  }

  return (
    <>
      {/* Mobile Filter Button */}
      <div className="lg:hidden mb-6">
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={() => setIsOpen(true)}
          className="w-full flex items-center justify-center space-x-2 bg-white border border-gray-200 rounded-xl py-3 px-4 shadow-sm"
        >
          <Filter className="w-5 h-5 text-gray-600" />
          <span className="font-medium text-gray-900">Filtri</span>
        </motion.button>
      </div>

      {/* Desktop Filters */}
      <div className="hidden lg:block">
        <FilterContent
          filters={filters}
          categories={categories}
          locations={locations}
          dateRanges={dateRanges}
          getCategoryIcon={getCategoryIcon}
          onFilterUpdate={handleFilterUpdate}
          onClearFilters={clearFilters}
        />
      </div>

      {/* Mobile Filter Modal */}
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="lg:hidden fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-end"
        >
          <motion.div
            initial={{ y: '100%' }}
            animate={{ y: 0 }}
            exit={{ y: '100%' }}
            className="bg-white rounded-t-2xl w-full max-h-[80vh] overflow-y-auto"
          >
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Filtri</h3>
              <button
                onClick={() => setIsOpen(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
            <div className="p-6">
              <FilterContent
                filters={filters}
                categories={categories}
                locations={locations}
                dateRanges={dateRanges}
                getCategoryIcon={getCategoryIcon}
                onFilterUpdate={handleFilterUpdate}
                onClearFilters={clearFilters}
              />
              <div className="mt-6 pt-6 border-t border-gray-200">
                <button
                  onClick={() => setIsOpen(false)}
                  className="w-full btn-primary"
                >
                  Primijeni filtere
                </button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </>
  )
}

const FilterContent = ({ 
  filters, 
  categories, 
  locations, 
  dateRanges,
  getCategoryIcon,
  onFilterUpdate, 
  onClearFilters 
}: any) => {
  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">Filtri</h3>
        <button
          onClick={onClearFilters}
          className="text-primary hover:text-accent-700 text-sm font-medium transition-colors"
        >
          Očisti sve
        </button>
      </div>

      {/* Featured Toggle */}
      <div className="space-y-3">
        <motion.button
          whileHover={{ scale: 1.01 }}
          whileTap={{ scale: 0.99 }}
          onClick={() => onFilterUpdate('featured', !filters.featured)}
          className={`w-full p-4 rounded-xl border-2 transition-all duration-300 flex items-center space-x-3 ${
            filters.featured
              ? 'border-primary bg-primary/10'
              : 'border-gray-200 hover:border-primary/50'
          }`}
        >
          <div className={`w-10 h-10 rounded-xl flex items-center justify-center ${
            filters.featured ? 'bg-primary text-white' : 'bg-gray-100 text-gray-600'
          }`}>
            <Star className="w-5 h-5" />
          </div>
          <div className="flex-1 text-left">
            <div className={`font-semibold ${
              filters.featured ? 'text-primary' : 'text-gray-900'
            }`}>
              Samo istaknuti događaji
            </div>
            <div className={`text-sm ${
              filters.featured ? 'text-primary/70' : 'text-gray-600'
            }`}>
              Prikaži samo preporučene događaje
            </div>
          </div>
        </motion.button>
      </div>

      {/* Category */}
      <div className="space-y-3">
        <label className="block text-sm font-medium text-gray-700">
          Kategorija
        </label>
        <div className="grid grid-cols-2 gap-3">
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => onFilterUpdate('category', '')}
            className={`p-3 rounded-lg border transition-all duration-300 text-center ${
              filters.category === ''
                ? 'border-primary bg-primary/10 text-primary'
                : 'border-gray-200 hover:border-primary/50 text-gray-700'
            }`}
          >
            <div className="font-medium text-sm">Sve</div>
          </motion.button>
          
          {categories.map((category) => {
            const Icon = getCategoryIcon(category)
            return (
              <motion.button
                key={category}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => onFilterUpdate('category', category)}
                className={`p-3 rounded-lg border transition-all duration-300 ${
                  filters.category === category
                    ? 'border-primary bg-primary/10 text-primary'
                    : 'border-gray-200 hover:border-primary/50 text-gray-700'
                }`}
              >
                <Icon className="w-5 h-5 mx-auto mb-1" />
                <div className="font-medium text-sm">{category}</div>
              </motion.button>
            )
          })}
        </div>
      </div>

      {/* Location */}
      <div className="space-y-3">
        <label className="block text-sm font-medium text-gray-700">
          Lokacija
        </label>
        <div className="relative">
          <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <select
            value={filters.location}
            onChange={(e) => onFilterUpdate('location', e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
          >
            <option value="">Sve lokacije</option>
            {locations.map((location) => (
              <option key={location} value={location}>
                {location}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Date Range */}
      <div className="space-y-3">
        <label className="block text-sm font-medium text-gray-700">
          Datum
        </label>
        <div className="space-y-2">
          {dateRanges.map((range) => (
            <motion.button
              key={range.id}
              whileHover={{ scale: 1.01 }}
              whileTap={{ scale: 0.99 }}
              onClick={() => onFilterUpdate('dateRange', range.id)}
              className={`w-full p-3 rounded-lg border transition-all duration-300 flex items-center space-x-3 ${
                filters.dateRange === range.id
                  ? 'border-primary bg-primary/10 text-primary'
                  : 'border-gray-200 hover:border-primary/50 text-gray-700'
              }`}
            >
              <Calendar className="w-4 h-4" />
              <span className="font-medium">{range.name}</span>
            </motion.button>
          ))}
        </div>
      </div>

      {/* Price Range */}
      <div className="space-y-3">
        <label className="block text-sm font-medium text-gray-700">
          Cijena
        </label>
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>€{filters.priceRange[0]}</span>
            <span>€{filters.priceRange[1]}</span>
          </div>
          <input
            type="range"
            min="0"
            max="200"
            step="5"
            value={filters.priceRange[1]}
            onChange={(e) => onFilterUpdate('priceRange', [filters.priceRange[0], parseInt(e.target.value)])}
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            style={{
              background: `linear-gradient(to right, #ad795a 0%, #ad795a ${(filters.priceRange[1] / 200) * 100}%, #e5e7eb ${(filters.priceRange[1] / 200) * 100}%, #e5e7eb 100%)`
            }}
          />
          <div className="flex items-center space-x-2">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => onFilterUpdate('priceRange', [0, 0])}
              className={`px-3 py-1 rounded-lg text-xs font-medium transition-colors ${
                filters.priceRange[1] === 0
                  ? 'bg-primary text-white'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              Besplatno
            </motion.button>
          </div>
        </div>
      </div>

      {/* Quick Filters */}
      <div className="space-y-3">
        <label className="block text-sm font-medium text-gray-700">
          Brzi filtri
        </label>
        <div className="flex flex-wrap gap-2">
          {[
            { label: 'Ovaj vikend', action: () => onFilterUpdate('dateRange', 'week') },
            { label: 'Besplatno', action: () => onFilterUpdate('priceRange', [0, 0]) },
            { label: 'Zagreb', action: () => onFilterUpdate('location', 'Zagreb') },
            { label: 'Glazba', action: () => onFilterUpdate('category', 'Glazba') }
          ].map((quickFilter, index) => (
            <motion.button
              key={index}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={quickFilter.action}
              className="bg-gray-100 hover:bg-primary hover:text-white text-gray-700 px-3 py-1 rounded-lg text-sm font-medium transition-all duration-300"
            >
              {quickFilter.label}
            </motion.button>
          ))}
        </div>
      </div>
    </div>
  )
}

export default EventsFilter
