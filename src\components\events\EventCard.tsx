'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'
import { 
  Calendar, 
  MapPin, 
  Euro, 
  Users, 
  Star,
  Clock,
  Plus,
  ExternalLink,
  Heart
} from 'lucide-react'
import { useState } from 'react'

interface EventCardProps {
  event: {
    id: number
    title: string
    category: string
    location: string
    date: string
    endDate?: string
    price: number
    image: string
    description: string
    featured: boolean
    tags: string[]
    organizer: string
    capacity: number
    attendees: number
  }
}

const EventCard = ({ event }: EventCardProps) => {
  const [isFavorite, setIsFavorite] = useState(false)
  const [imageLoaded, setImageLoaded] = useState(false)

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('hr-HR', {
      day: 'numeric',
      month: 'short'
    })
  }

  const getDateRange = () => {
    if (event.endDate && event.endDate !== event.date) {
      return `${formatDate(event.date)} - ${formatDate(event.endDate)}`
    }
    return formatDate(event.date)
  }

  const attendancePercentage = (event.attendees / event.capacity) * 100

  return (
    <motion.div
      whileHover={{ y: -5 }}
      className="card overflow-hidden group cursor-pointer"
    >
      {/* Image */}
      <div className="relative h-48 overflow-hidden">
        <Image
          src={event.image}
          alt={event.title}
          fill
          className={`object-cover group-hover:scale-110 transition-transform duration-500 ${
            imageLoaded ? 'opacity-100' : 'opacity-0'
          }`}
          onLoad={() => setImageLoaded(true)}
        />
        
        {/* Loading placeholder */}
        {!imageLoaded && (
          <div className="absolute inset-0 bg-gray-200 animate-pulse" />
        )}

        {/* Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />

        {/* Featured Badge */}
        {event.featured && (
          <div className="absolute top-3 left-3 bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-3 py-1 rounded-full text-xs font-bold flex items-center space-x-1">
            <Star className="w-3 h-3 fill-current" />
            <span>Istaknuto</span>
          </div>
        )}

        {/* Favorite Button */}
        <motion.button
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={(e) => {
            e.stopPropagation()
            setIsFavorite(!isFavorite)
          }}
          className="absolute top-3 right-3 w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-sm"
        >
          <Heart 
            className={`w-4 h-4 transition-colors ${
              isFavorite ? 'text-red-500 fill-current' : 'text-gray-600'
            }`} 
          />
        </motion.button>

        {/* Price Badge */}
        <div className="absolute bottom-3 right-3 bg-primary text-white px-3 py-1 rounded-lg font-semibold">
          {event.price === 0 ? 'Besplatno' : `€${event.price}`}
        </div>

        {/* Category Badge */}
        <div className="absolute bottom-3 left-3 bg-white/90 backdrop-blur-sm text-gray-900 px-3 py-1 rounded-lg text-sm font-medium">
          {event.category}
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Header */}
        <div className="mb-4">
          <h3 className="text-lg font-bold text-gray-900 mb-2 group-hover:text-primary transition-colors line-clamp-2">
            {event.title}
          </h3>
          <p className="text-gray-600 text-sm line-clamp-2">
            {event.description}
          </p>
        </div>

        {/* Event Details */}
        <div className="space-y-3 mb-4">
          <div className="flex items-center space-x-2 text-gray-600 text-sm">
            <Calendar className="w-4 h-4" />
            <span>{getDateRange()}</span>
          </div>
          
          <div className="flex items-center space-x-2 text-gray-600 text-sm">
            <MapPin className="w-4 h-4" />
            <span>{event.location}</span>
          </div>
          
          <div className="flex items-center space-x-2 text-gray-600 text-sm">
            <Clock className="w-4 h-4" />
            <span>Organizator: {event.organizer}</span>
          </div>
        </div>

        {/* Tags */}
        <div className="flex flex-wrap gap-2 mb-4">
          {event.tags.slice(0, 3).map((tag, index) => (
            <span
              key={index}
              className="bg-secondary text-primary px-2 py-1 rounded-full text-xs font-medium"
            >
              {tag}
            </span>
          ))}
          {event.tags.length > 3 && (
            <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">
              +{event.tags.length - 3}
            </span>
          )}
        </div>

        {/* Attendance */}
        <div className="mb-4">
          <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
            <span>Prijavljeni</span>
            <span>{event.attendees}/{event.capacity}</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <motion.div
              initial={{ width: 0 }}
              animate={{ width: `${attendancePercentage}%` }}
              transition={{ duration: 1, delay: 0.5 }}
              className={`h-2 rounded-full ${
                attendancePercentage > 80 
                  ? 'bg-red-500' 
                  : attendancePercentage > 60 
                    ? 'bg-yellow-500' 
                    : 'bg-green-500'
              }`}
            />
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-100">
          <div className="flex items-center space-x-2">
            <Users className="w-4 h-4 text-gray-400" />
            <span className="text-sm text-gray-600">
              {event.attendees} prijavljenih
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="text-primary hover:text-accent-700 transition-colors"
            >
              <ExternalLink className="w-4 h-4" />
            </motion.button>
            
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="flex items-center space-x-2 bg-primary text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-accent-700 transition-colors"
            >
              <Plus className="w-4 h-4" />
              <span>Dodaj u plan</span>
            </motion.button>
          </div>
        </div>
      </div>
    </motion.div>
  )
}

export default EventCard
