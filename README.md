# Eventria AI Travel Planner 🇭🇷

Breathtaking AI-powered travel planner web application in Croatian language, focused on creating seamless, customized trip/travel experiences, team building opportunities and sport tournament organization services using advanced AI features.

## 🌟 Features

### 🏠 Homepage
- **Hero Section**: Compelling tagline "<PERSON>a<PERSON><PERSON> snove putovanje ili događaj. Planiran od strane AI." with gradient backdrop
- **3D Globe Animation**: Dynamic globe with floating trip cards and location pins
- **How It Works**: 3-step guide with animated icons
- **Destinations Showcase**: Croatian regions (Baranja, Central Croatia, Istria, Dalmatia)
- **Testimonials Carousel**: Real user experiences
- **Responsive Design**: Ultra-smooth across mobile and desktop

### 🗺️ AI Trip Planner
- **Multi-step Wizard**: 6-step process for creating personalized itineraries
  1. **Destinations**: Select Croatian destinations with search and visual cards
  2. **Dates**: Flexible or fixed dates with quick selection options
  3. **Interests**: Activity preferences with categorized options
  4. **Budget**: Customizable budget with currency selection (EUR, USD, HRK)
  5. **Preferences**: Travel pace, group size, accessibility, weather preferences
  6. **Review**: Final overview before AI generation

- **AI-Powered Generation**: Uses OpenRouter API with Claude 3.5 Sonnet for Croatian travel expertise
- **Interactive Results**: Day-by-day itinerary with draggable cards and embedded maps
- **EventriaBot Chat**: Live editing through AI chat interface

### 🏨 Hotels Explorer
- **20+ Hotel Cards**: Detailed hotel information with proper filters
- **Advanced Filtering**: Price, distance, amenities, room type, ratings
- **Map View**: Interactive map with hotel locations
- **Detailed Pages**: Photo galleries, booking links, reviews, AI-generated highlights

### 🎭 Events Page
- **Dynamic Feed**: 20+ local cultural events, festivals, and hidden gems
- **Smart Filtering**: Category, location, date range, price filters
- **Event Details**: Photos, dates, descriptions, maps, "Add to Trip" functionality
- **Featured Events**: Highlighted recommendations

### 📊 Dashboard
- **Trip Management**: All saved trips with status tracking
- **Timeline Editor**: Drag-and-drop itinerary editing
- **Collaboration**: Share trips with friends and team members
- **Analytics**: Personal travel statistics and insights
- **Recent Activity**: Track all user actions and modifications

### 🤖 AI Features
- **OpenRouter Integration**: Multiple AI models including free options
- **Croatian Language**: Native Croatian language support
- **Context-Aware Suggestions**: "Add sunset point to Day 3?" or "Swap museum for wine tour?"
- **Real-time Editing**: Live itinerary modifications through chat
- **Smart Recommendations**: Personalized suggestions based on preferences

## 🎨 Design System

### Colors
- **Primary**: #ad795a (Warm brown)
- **Secondary**: #f6f0e9 (Cream)
- **Accent Gradients**: Various warm tones for visual hierarchy

### Typography
- **Font**: Poppins (Google Fonts)
- **Weights**: 300, 400, 500, 600, 700

### UI Elements
- **Glassmorphism**: Soft-glass UI with backdrop blur effects
- **Floating Cards**: Elevated design with gentle shadows
- **Smooth Transitions**: Framer Motion animations throughout
- **Micro-interactions**: Hover effects, loading states, tooltips

## 🛠️ Technology Stack

### Frontend
- **Next.js 14**: React framework with App Router
- **TypeScript**: Type-safe development
- **Tailwind CSS**: Utility-first styling
- **Framer Motion**: Smooth animations and transitions
- **Lottie React**: Micro-animations and interactive elements

### AI Integration
- **OpenRouter API**: Multiple AI model access
- **Claude 3.5 Sonnet**: Primary model for Croatian language support
- **API Key**: 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

### Additional Libraries
- **Lucide React**: Beautiful icons
- **React Hook Form**: Form management
- **Zod**: Schema validation
- **Date-fns**: Date manipulation
- **Mapbox GL**: Interactive maps

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd eventria-ai-planner
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Open browser**
   Navigate to `http://localhost:3000`

### Environment Variables
Create a `.env.local` file:
```env
OPENROUTER_API_KEY=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
NEXT_PUBLIC_MAPBOX_TOKEN=your_mapbox_token
```

## 📱 Features Overview

### Navigation
- **Smart Navbar**: Transparent on homepage, glassmorphic on other pages
- **Responsive Menu**: Mobile-friendly with smooth animations
- **Scroll Behavior**: Shrinks and becomes sticky on scroll

### Homepage Sections
1. **Hero**: AI tagline with 3D globe and floating cards
2. **How It Works**: 3-step process explanation
3. **Destinations**: Croatian regions showcase
4. **Testimonials**: User experience carousel
5. **CTA**: Final call-to-action with features list

### Planner Wizard
1. **Destination Selection**: Visual cards with search functionality
2. **Date Selection**: Flexible or fixed with quick options
3. **Interest Selection**: Categorized activity preferences
4. **Budget Configuration**: Range sliders with currency options
5. **Preferences Setup**: Travel pace, group size, accessibility
6. **Review & Generate**: Final overview and AI generation

### AI Chat Integration
- **EventriaBot**: Conversational AI for itinerary modifications
- **Quick Actions**: Pre-defined common requests
- **Real-time Updates**: Live itinerary modifications
- **Context Awareness**: Understands current trip details

## 🌍 Croatian Focus

### Destinations
- **Dubrovnik**: Pearl of the Adriatic
- **Split**: Diocletian's Palace
- **Zagreb**: Capital city culture
- **Plitvice Lakes**: National park
- **Rovinj**: Romantic Istrian town
- **Hvar**: Lavender island
- **Zadar**: Sea Organ city
- **Kopački Rit**: Baranja nature park

### Cultural Integration
- **Local Events**: Croatian festivals and cultural events
- **Traditional Cuisine**: Restaurant recommendations
- **Hidden Gems**: Off-the-beaten-path locations
- **Seasonal Activities**: Weather-appropriate suggestions
- **Local Tips**: Insider knowledge and cultural insights

## 🔧 Development

### Project Structure
```
src/
├── app/                 # Next.js 14 App Router
│   ├── layout.tsx      # Root layout
│   ├── page.tsx        # Homepage
│   ├── planner/        # Trip planner
│   ├── hotels/         # Hotels explorer
│   ├── events/         # Events page
│   ├── dashboard/      # User dashboard
│   └── api/            # API routes
├── components/         # Reusable components
│   ├── ui/             # Base UI components
│   ├── layout/         # Layout components
│   ├── sections/       # Page sections
│   ├── planner/        # Planner components
│   ├── hotels/         # Hotel components
│   ├── events/         # Event components
│   └── dashboard/      # Dashboard components
├── lib/                # Utilities
│   ├── ai/             # AI integration
│   └── utils/          # Helper functions
└── styles/             # Global styles
```

### Key Components
- **PlannerWizard**: Multi-step form with validation
- **ItineraryResult**: AI-generated trip display
- **EventriaBot**: AI chat interface
- **HotelCard**: Hotel information display
- **EventCard**: Event information display
- **TripCard**: Dashboard trip management

## 🎯 Future Enhancements

### Planned Features
- **Mobile App**: React Native version
- **Offline Mode**: Cached itineraries
- **Social Features**: Trip sharing and reviews
- **Payment Integration**: Direct booking capabilities
- **Advanced Analytics**: Detailed travel insights
- **Multi-language**: Support for other languages

### AI Improvements
- **Voice Interface**: Voice-controlled planning
- **Image Recognition**: Photo-based recommendations
- **Predictive Analytics**: Trend-based suggestions
- **Personalization**: Learning user preferences

## 📄 License

This project is proprietary software developed for Eventria AI Travel Planner.

## 🤝 Contributing

This is a private project. For questions or suggestions, please contact the development team.

---

**Eventria AI Travel Planner** - Your dream trip or event, planned by AI. 🚀
