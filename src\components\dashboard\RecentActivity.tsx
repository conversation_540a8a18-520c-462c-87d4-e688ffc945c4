'use client'

import { motion } from 'framer-motion'
import { LucideIcon } from 'lucide-react'

interface Activity {
  id: number
  type: string
  title: string
  description: string
  timestamp: string
  icon: LucideIcon
}

interface RecentActivityProps {
  activities: Activity[]
}

const RecentActivity = ({ activities }: RecentActivityProps) => {
  const formatTimeAgo = (timestamp: string) => {
    const now = new Date()
    const activityTime = new Date(timestamp)
    const diffInHours = Math.floor((now.getTime() - activityTime.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return 'Prije manje od sata'
    if (diffInHours < 24) return `Prije ${diffInHours}h`
    
    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays < 7) return `Prije ${diffInDays} dana`
    
    return activityTime.toLocaleDateString('hr-HR')
  }

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'trip_created': return 'from-blue-500 to-cyan-500'
      case 'event_saved': return 'from-green-500 to-emerald-500'
      case 'trip_shared': return 'from-purple-500 to-indigo-500'
      default: return 'from-gray-500 to-slate-500'
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.2 }}
      className="card p-6"
    >
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Nedavna aktivnost
      </h3>
      
      <div className="space-y-4">
        {activities.map((activity, index) => {
          const Icon = activity.icon
          const colorClass = getActivityColor(activity.type)
          
          return (
            <motion.div
              key={activity.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className="flex items-start space-x-3"
            >
              <div className={`w-10 h-10 rounded-xl bg-gradient-to-br ${colorClass} flex items-center justify-center flex-shrink-0`}>
                <Icon className="w-5 h-5 text-white" />
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="font-medium text-gray-900 text-sm">
                  {activity.title}
                </div>
                <div className="text-gray-600 text-sm truncate">
                  {activity.description}
                </div>
                <div className="text-gray-500 text-xs mt-1">
                  {formatTimeAgo(activity.timestamp)}
                </div>
              </div>
            </motion.div>
          )
        })}
      </div>
      
      <motion.button
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        className="w-full mt-4 text-primary hover:text-accent-700 text-sm font-medium transition-colors"
      >
        Vidi sve aktivnosti
      </motion.button>
    </motion.div>
  )
}

export default RecentActivity
