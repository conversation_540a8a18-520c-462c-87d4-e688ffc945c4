'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'
import { 
  Calendar, 
  MapPin, 
  Users, 
  Euro,
  Edit3,
  Share2,
  Download,
  MoreHorizontal,
  Clock,
  CheckCircle,
  FileText
} from 'lucide-react'
import { useState } from 'react'

interface TripCardProps {
  trip: {
    id: number
    title: string
    destination: string
    dates: string
    status: 'planned' | 'draft' | 'completed'
    budget: number
    image: string
    activities: number
    days: number
    travelers: number
    lastModified: string
  }
}

const TripCard = ({ trip }: TripCardProps) => {
  const [showMenu, setShowMenu] = useState(false)

  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'planned':
        return {
          label: 'Planirano',
          color: 'bg-green-100 text-green-800',
          icon: CheckCircle
        }
      case 'draft':
        return {
          label: 'Nacrt',
          color: 'bg-yellow-100 text-yellow-800',
          icon: FileText
        }
      case 'completed':
        return {
          label: '<PERSON>avršeno',
          color: 'bg-blue-100 text-blue-800',
          icon: CheckCircle
        }
      default:
        return {
          label: 'Nepoznato',
          color: 'bg-gray-100 text-gray-800',
          icon: FileText
        }
    }
  }

  const statusConfig = getStatusConfig(trip.status)
  const StatusIcon = statusConfig.icon

  return (
    <motion.div
      whileHover={{ y: -2 }}
      className="card overflow-hidden group"
    >
      <div className="md:flex">
        {/* Image */}
        <div className="md:w-48 h-48 md:h-auto relative overflow-hidden">
          <Image
            src={trip.image}
            alt={trip.title}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-300"
          />
          
          {/* Status Badge */}
          <div className="absolute top-3 left-3">
            <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${statusConfig.color}`}>
              <StatusIcon className="w-3 h-3" />
              <span>{statusConfig.label}</span>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 p-6">
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <h3 className="text-lg font-bold text-gray-900 mb-2 group-hover:text-primary transition-colors">
                {trip.title}
              </h3>
              
              <div className="space-y-2 text-sm text-gray-600">
                <div className="flex items-center space-x-2">
                  <MapPin className="w-4 h-4" />
                  <span>{trip.destination}</span>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Calendar className="w-4 h-4" />
                  <span>{trip.dates}</span>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Clock className="w-4 h-4" />
                  <span>Zadnja izmjena: {new Date(trip.lastModified).toLocaleDateString('hr-HR')}</span>
                </div>
              </div>
            </div>

            {/* Menu */}
            <div className="relative">
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => setShowMenu(!showMenu)}
                className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <MoreHorizontal className="w-5 h-5 text-gray-400" />
              </motion.button>

              {showMenu && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="absolute right-0 top-full mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-10"
                >
                  {[
                    { icon: Edit3, label: 'Uredi', color: 'text-gray-700' },
                    { icon: Share2, label: 'Podijeli', color: 'text-gray-700' },
                    { icon: Download, label: 'Izvezi PDF', color: 'text-gray-700' },
                  ].map((item, index) => {
                    const Icon = item.icon
                    return (
                      <motion.button
                        key={index}
                        whileHover={{ backgroundColor: '#f3f4f6' }}
                        className="w-full flex items-center space-x-3 px-4 py-2 text-left transition-colors"
                      >
                        <Icon className={`w-4 h-4 ${item.color}`} />
                        <span className={`${item.color}`}>{item.label}</span>
                      </motion.button>
                    )
                  })}
                </motion.div>
              )}
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
            <div className="text-center">
              <div className="text-lg font-bold text-primary">{trip.days}</div>
              <div className="text-xs text-gray-600">Dana</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-primary">{trip.activities}</div>
              <div className="text-xs text-gray-600">Aktivnosti</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-primary">{trip.travelers}</div>
              <div className="text-xs text-gray-600">Putnika</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-primary">€{trip.budget}</div>
              <div className="text-xs text-gray-600">Budžet</div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center justify-between pt-4 border-t border-gray-100">
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <Users className="w-4 h-4" />
              <span>{trip.travelers} {trip.travelers === 1 ? 'putnik' : 'putnika'}</span>
            </div>
            
            <div className="flex items-center space-x-2">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="text-gray-600 hover:text-primary transition-colors"
              >
                <Share2 className="w-4 h-4" />
              </motion.button>
              
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-primary text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-accent-700 transition-colors"
              >
                {trip.status === 'draft' ? 'Nastavi uređivanje' : 'Otvori'}
              </motion.button>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  )
}

export default TripCard
