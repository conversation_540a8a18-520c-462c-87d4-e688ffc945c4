'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { MapPin, Star, Euro, ExternalLink } from 'lucide-react'

interface HotelMapProps {
  hotels: Array<{
    id: number
    name: string
    location: string
    rating: number
    price: number
    image: string
    amenities: string[]
    roomType: string
    distance: number
    description: string
    highlights: string[]
  }>
}

const HotelMap = ({ hotels }: HotelMapProps) => {
  const [selectedHotel, setSelectedHotel] = useState<number | null>(null)

  // Mock coordinates for Croatian cities
  const cityCoordinates: { [key: string]: { lat: number; lng: number } } = {
    'Zagreb': { lat: 45.8150, lng: 15.9819 },
    'Split': { lat: 43.5081, lng: 16.4402 },
    'Dubrovnik': { lat: 42.6507, lng: 18.0944 },
    'Rovinj': { lat: 45.0811, lng: 13.6387 },
    'Hvar': { lat: 43.1729, lng: 16.4414 },
    'Zadar': { lat: 44.1194, lng: 15.2314 },
    'Pula': { lat: 44.8666, lng: 13.8496 },
    'Rijeka': { lat: 45.3271, lng: 14.4422 },
    'Baska Voda': { lat: 43.3564, lng: 16.9511 }
  }

  // Calculate position for each hotel based on city coordinates
  const hotelsWithPositions = hotels.map(hotel => ({
    ...hotel,
    position: cityCoordinates[hotel.location] || { lat: 45.1, lng: 15.2 }
  }))

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
      {/* Map Container */}
      <div className="relative h-96 bg-gradient-to-br from-blue-50 to-green-50">
        {/* Croatia Map Outline (Simplified) */}
        <svg
          viewBox="0 0 800 400"
          className="absolute inset-0 w-full h-full"
        >
          {/* Simplified Croatia outline */}
          <path
            d="M100 200 L200 180 L300 160 L400 150 L500 140 L600 160 L650 180 L700 200 L680 250 L650 280 L600 300 L500 320 L400 310 L300 300 L200 280 L150 250 Z"
            fill="rgba(173, 121, 90, 0.1)"
            stroke="rgba(173, 121, 90, 0.3)"
            strokeWidth="2"
          />
          
          {/* Coastline details */}
          <path
            d="M400 150 L420 170 L440 160 L460 180 L480 170 L500 190 L520 180 L540 200 L560 190 L580 210 L600 200"
            fill="none"
            stroke="rgba(173, 121, 90, 0.3)"
            strokeWidth="1"
          />
        </svg>

        {/* Hotel Markers */}
        {hotelsWithPositions.map((hotel, index) => {
          // Convert lat/lng to SVG coordinates (simplified)
          const x = ((hotel.position.lng - 13) / (19 - 13)) * 800
          const y = ((46 - hotel.position.lat) / (46 - 42)) * 400
          
          return (
            <motion.div
              key={hotel.id}
              initial={{ scale: 0, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: index * 0.1 }}
              className="absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer"
              style={{ left: `${x}px`, top: `${y}px` }}
              onClick={() => setSelectedHotel(selectedHotel === hotel.id ? null : hotel.id)}
            >
              <motion.div
                whileHover={{ scale: 1.2 }}
                whileTap={{ scale: 0.9 }}
                className={`w-8 h-8 rounded-full flex items-center justify-center shadow-lg transition-all duration-300 ${
                  selectedHotel === hotel.id
                    ? 'bg-primary text-white ring-4 ring-primary/30'
                    : 'bg-white text-primary border-2 border-primary hover:bg-primary hover:text-white'
                }`}
              >
                <MapPin className="w-4 h-4" />
              </motion.div>
              
              {/* Price Label */}
              <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-white text-primary px-2 py-1 rounded-lg text-xs font-semibold shadow-sm whitespace-nowrap">
                €{hotel.price}
              </div>
            </motion.div>
          )
        })}

        {/* Legend */}
        <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-sm">
          <h4 className="font-semibold text-gray-900 mb-2">Legenda</h4>
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <div className="w-4 h-4 bg-primary rounded-full"></div>
            <span>Hotel lokacije</span>
          </div>
        </div>

        {/* Map Controls */}
        <div className="absolute top-4 right-4 flex flex-col space-y-2">
          <button className="bg-white/90 backdrop-blur-sm p-2 rounded-lg shadow-sm hover:bg-white transition-colors">
            <span className="text-lg font-bold text-gray-600">+</span>
          </button>
          <button className="bg-white/90 backdrop-blur-sm p-2 rounded-lg shadow-sm hover:bg-white transition-colors">
            <span className="text-lg font-bold text-gray-600">−</span>
          </button>
        </div>
      </div>

      {/* Selected Hotel Details */}
      {selectedHotel && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="p-6 border-t border-gray-200"
        >
          {(() => {
            const hotel = hotels.find(h => h.id === selectedHotel)
            if (!hotel) return null
            
            return (
              <div className="flex items-start space-x-4">
                <div className="w-20 h-20 rounded-lg overflow-hidden flex-shrink-0">
                  <img
                    src={hotel.image}
                    alt={hotel.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                
                <div className="flex-1">
                  <div className="flex items-start justify-between mb-2">
                    <div>
                      <h3 className="text-lg font-bold text-gray-900">{hotel.name}</h3>
                      <div className="flex items-center space-x-1 text-gray-600 text-sm">
                        <MapPin className="w-4 h-4" />
                        <span>{hotel.location}</span>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-1 bg-yellow-50 px-2 py-1 rounded-lg">
                      <Star className="w-4 h-4 text-yellow-500 fill-current" />
                      <span className="text-sm font-semibold text-gray-900">{hotel.rating}</span>
                    </div>
                  </div>
                  
                  <p className="text-gray-600 text-sm mb-3">{hotel.description}</p>
                  
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-600">
                      <span>Od </span>
                      <span className="text-lg font-bold text-primary">€{hotel.price}</span>
                      <span> / noć</span>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className="text-primary hover:text-accent-700 transition-colors"
                      >
                        <ExternalLink className="w-4 h-4" />
                      </motion.button>
                      
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className="bg-primary text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-accent-700 transition-colors"
                      >
                        Rezerviraj
                      </motion.button>
                    </div>
                  </div>
                </div>
              </div>
            )
          })()}
        </motion.div>
      )}

      {/* Hotels List */}
      <div className="p-6 border-t border-gray-200">
        <h4 className="font-semibold text-gray-900 mb-4">
          Svi hoteli ({hotels.length})
        </h4>
        <div className="space-y-3 max-h-60 overflow-y-auto">
          {hotels.map((hotel) => (
            <motion.div
              key={hotel.id}
              whileHover={{ scale: 1.01 }}
              onClick={() => setSelectedHotel(hotel.id)}
              className={`flex items-center justify-between p-3 rounded-lg cursor-pointer transition-all duration-300 ${
                selectedHotel === hotel.id
                  ? 'bg-primary/10 border border-primary/20'
                  : 'bg-gray-50 hover:bg-gray-100'
              }`}
            >
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <div>
                  <div className="font-medium text-gray-900">{hotel.name}</div>
                  <div className="text-sm text-gray-600">{hotel.location}</div>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <div className="flex items-center space-x-1">
                  <Star className="w-3 h-3 text-yellow-500 fill-current" />
                  <span className="text-sm text-gray-600">{hotel.rating}</span>
                </div>
                <div className="text-sm font-semibold text-primary">
                  €{hotel.price}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default HotelMap
