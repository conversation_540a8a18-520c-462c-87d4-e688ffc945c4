'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  ArrowLeft,
  Download,
  Share2,
  Edit3,
  MapPin,
  Clock,
  Euro,
  Calendar,
  MessageSquare,
  Save,
  ExternalLink
} from 'lucide-react'
import ItineraryDay from './ItineraryDay'
import EventriaBot from './EventriaBot'

interface ItineraryResultProps {
  data: any
  onBack: () => void
}

const ItineraryResult = ({ data, onBack }: ItineraryResultProps) => {
  const [itinerary, setItinerary] = useState(null)
  const [loading, setLoading] = useState(true)
  const [showBot, setShowBot] = useState(false)
  const [activeDay, setActiveDay] = useState(0)

  // Real AI itinerary generation using OpenRouter API
  useEffect(() => {
    const generateItinerary = async () => {
      setLoading(true)

      try {
        const response = await fetch('/api/generate-itinerary', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data)
        })

        if (!response.ok) {
          throw new Error('Failed to generate itinerary')
        }

        const result = await response.json()
        setItinerary(result.itinerary)
      } catch (error) {
        console.error('Error generating itinerary:', error)
        // Fallback to mock data if API fails
        const mockItinerary = {
        title: `Putovanje po ${data.destinations?.[0] || 'Hrvatskoj'}`,
        duration: data.dates?.flexible ? data.dates.duration :
          Math.ceil((new Date(data.dates?.endDate).getTime() - new Date(data.dates?.startDate).getTime()) / (1000 * 60 * 60 * 24)),
        totalBudget: data.budget?.max || 1000,
        currency: data.budget?.currency || 'EUR',
        days: [
          {
            day: 1,
            date: '2024-06-15',
            title: 'Dolazak i istraživanje',
            activities: [
              {
                time: '09:00',
                title: 'Dolazak u Zagreb',
                description: 'Dolazak na aerodrom i transfer do hotela',
                location: 'Aerodrom Zagreb',
                duration: '2h',
                cost: 50,
                type: 'transport'
              },
              {
                time: '12:00',
                title: 'Ručak u Dolac tržnici',
                description: 'Tradicionalni hrvatski ručak na glavnoj tržnici',
                location: 'Dolac tržnica',
                duration: '1.5h',
                cost: 25,
                type: 'food'
              },
              {
                time: '14:00',
                title: 'Obilazak Gornjeg grada',
                description: 'Šetnja kroz povijesni centar Zagreba',
                location: 'Gornji grad',
                duration: '3h',
                cost: 0,
                type: 'sightseeing'
              }
            ]
          },
          {
            day: 2,
            date: '2024-06-16',
            title: 'Priroda i kultura',
            activities: [
              {
                time: '08:00',
                title: 'Izlet na Plitvička jezera',
                description: 'Cjelodnevni izlet u nacionalni park',
                location: 'Plitvička jezera',
                duration: '8h',
                cost: 120,
                type: 'nature'
              },
              {
                time: '18:00',
                title: 'Povratak u Zagreb',
                description: 'Transfer natrag u grad',
                location: 'Zagreb',
                duration: '2h',
                cost: 40,
                type: 'transport'
              }
            ]
          }
        ]
        }

        setItinerary(mockItinerary)
      }
      setLoading(false)
    }

    generateItinerary()
  }, [data])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center"
        >
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full mx-auto mb-6"
          />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            AI kreira vaš itinerar...
          </h2>
          <p className="text-gray-600">
            Analiziramo vaše preferencije i kreiramo savršeno putovanje
          </p>
          <div className="mt-8 space-y-2 text-sm text-gray-500">
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 }}
            >
              ✓ Analiziranje destinacija...
            </motion.div>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1.5 }}
            >
              ✓ Optimiziranje ruta...
            </motion.div>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 2.5 }}
            >
              ✓ Kreiranje itinerara...
            </motion.div>
          </div>
        </motion.div>
      </div>
    )
  }

  if (!itinerary) return null

  return (
    <div className="max-w-7xl mx-auto">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between mb-8"
      >
        <button
          onClick={onBack}
          className="flex items-center space-x-2 text-gray-600 hover:text-primary transition-colors"
        >
          <ArrowLeft className="w-5 h-5" />
          <span>Natrag na planer</span>
        </button>

        <div className="flex items-center space-x-3">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="flex items-center space-x-2 bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors"
          >
            <Save className="w-4 h-4" />
            <span>Spremi</span>
          </motion.button>

          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="flex items-center space-x-2 bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors"
          >
            <Share2 className="w-4 h-4" />
            <span>Podijeli</span>
          </motion.button>

          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="flex items-center space-x-2 btn-primary"
          >
            <Download className="w-4 h-4" />
            <span>Izvezi PDF</span>
          </motion.button>
        </div>
      </motion.div>

      {/* Itinerary Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="card p-8 mb-8"
      >
        <div className="grid md:grid-cols-2 gap-8">
          <div>
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              {itinerary.title}
            </h1>
            <div className="space-y-3 text-gray-600">
              <div className="flex items-center space-x-2">
                <Calendar className="w-5 h-5" />
                <span>{itinerary.duration} dana putovanja</span>
              </div>
              <div className="flex items-center space-x-2">
                <MapPin className="w-5 h-5" />
                <span>{data.destinations?.join(', ')}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Euro className="w-5 h-5" />
                <span>Budžet: €{itinerary.totalBudget} po osobi</span>
              </div>
            </div>
          </div>

          <div className="flex items-center justify-center">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setShowBot(true)}
              className="flex items-center space-x-3 bg-gradient-to-r from-primary to-accent-600 text-white px-6 py-4 rounded-xl shadow-lg"
            >
              <MessageSquare className="w-6 h-6" />
              <div className="text-left">
                <div className="font-semibold">EventriaBot</div>
                <div className="text-sm opacity-90">Prilagodi itinerar</div>
              </div>
            </motion.button>
          </div>
        </div>
      </motion.div>

      {/* Day Navigation */}
      <div className="flex space-x-2 mb-8 overflow-x-auto pb-2">
        {itinerary.days.map((day: any, index: number) => (
          <motion.button
            key={day.day}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => setActiveDay(index)}
            className={`flex-shrink-0 px-4 py-3 rounded-xl font-medium transition-all duration-300 ${
              activeDay === index
                ? 'bg-primary text-white shadow-lg'
                : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-200'
            }`}
          >
            Dan {day.day}
          </motion.button>
        ))}
      </div>

      {/* Itinerary Days */}
      <div className="space-y-8">
        {itinerary.days.map((day: any, index: number) => (
          <motion.div
            key={day.day}
            initial={{ opacity: 0, y: 20 }}
            animate={{
              opacity: activeDay === index ? 1 : 0.3,
              y: 0,
              scale: activeDay === index ? 1 : 0.95
            }}
            transition={{ duration: 0.3 }}
            className={activeDay === index ? 'block' : 'hidden md:block'}
          >
            <ItineraryDay day={day} />
          </motion.div>
        ))}
      </div>

      {/* EventriaBot Chat */}
      {showBot && (
        <EventriaBot
          itinerary={itinerary}
          onClose={() => setShowBot(false)}
          onUpdateItinerary={setItinerary}
        />
      )}
    </div>
  )
}

export default ItineraryResult
