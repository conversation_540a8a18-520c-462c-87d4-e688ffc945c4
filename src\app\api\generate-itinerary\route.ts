import { NextRequest, NextResponse } from 'next/server'
import { openRouterClient, ItineraryRequest } from '@/lib/ai/openrouter'

export async function POST(request: NextRequest) {
  try {
    const body: ItineraryRequest = await request.json()
    
    // Validate the request
    if (!body.destinations || body.destinations.length === 0) {
      return NextResponse.json(
        { error: 'Destinacije su obavezne' },
        { status: 400 }
      )
    }

    // Generate itinerary using OpenRouter
    const itinerary = await openRouterClient.generateItinerary(body)
    
    return NextResponse.json({
      success: true,
      itinerary
    })
  } catch (error) {
    console.error('Error generating itinerary:', error)
    
    return NextResponse.json(
      { 
        error: 'Greška pri kreiranju itinerara',
        details: error instanceof Error ? error.message : 'Nepoznata greška'
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Eventria AI Itinerary Generator',
    status: 'active',
    version: '1.0.0'
  })
}
