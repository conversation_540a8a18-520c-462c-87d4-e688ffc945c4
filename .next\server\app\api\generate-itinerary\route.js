"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-itinerary/route";
exports.ids = ["app/api/generate-itinerary/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-itinerary%2Froute&page=%2Fapi%2Fgenerate-itinerary%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-itinerary%2Froute.ts&appDir=C%3A%5CUsers%5Cdavid%5CDocuments%5CEventria%20AI%20Planner%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdavid%5CDocuments%5CEventria%20AI%20Planner&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-itinerary%2Froute&page=%2Fapi%2Fgenerate-itinerary%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-itinerary%2Froute.ts&appDir=C%3A%5CUsers%5Cdavid%5CDocuments%5CEventria%20AI%20Planner%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdavid%5CDocuments%5CEventria%20AI%20Planner&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_david_Documents_Eventria_AI_Planner_src_app_api_generate_itinerary_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/generate-itinerary/route.ts */ \"(rsc)/./src/app/api/generate-itinerary/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-itinerary/route\",\n        pathname: \"/api/generate-itinerary\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-itinerary/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Eventria AI Planner\\\\src\\\\app\\\\api\\\\generate-itinerary\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_david_Documents_Eventria_AI_Planner_src_app_api_generate_itinerary_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/generate-itinerary/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZhcGklMkZnZW5lcmF0ZS1pdGluZXJhcnklMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRmdlbmVyYXRlLWl0aW5lcmFyeSUyRnJvdXRlJmFwcFBhdGhzPSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmFwaSUyRmdlbmVyYXRlLWl0aW5lcmFyeSUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNkYXZpZCU1Q0RvY3VtZW50cyU1Q0V2ZW50cmlhJTIwQUklMjBQbGFubmVyJTVDc3JjJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1DJTNBJTVDVXNlcnMlNUNkYXZpZCU1Q0RvY3VtZW50cyU1Q0V2ZW50cmlhJTIwQUklMjBQbGFubmVyJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUFzRztBQUN2QztBQUNjO0FBQzZDO0FBQzFIO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixnSEFBbUI7QUFDM0M7QUFDQSxjQUFjLHlFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxZQUFZO0FBQ1osQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLFFBQVEsdUdBQXVHO0FBQy9HO0FBQ0E7QUFDQSxXQUFXLDRFQUFXO0FBQ3RCO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDNko7O0FBRTdKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXZlbnRyaWEtYWktcGxhbm5lci8/YTgyNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBcHBSb3V0ZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLW1vZHVsZXMvYXBwLXJvdXRlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IHBhdGNoRmV0Y2ggYXMgX3BhdGNoRmV0Y2ggfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9saWIvcGF0Y2gtZmV0Y2hcIjtcbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCJDOlxcXFxVc2Vyc1xcXFxkYXZpZFxcXFxEb2N1bWVudHNcXFxcRXZlbnRyaWEgQUkgUGxhbm5lclxcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFxnZW5lcmF0ZS1pdGluZXJhcnlcXFxccm91dGUudHNcIjtcbi8vIFdlIGluamVjdCB0aGUgbmV4dENvbmZpZ091dHB1dCBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgbmV4dENvbmZpZ091dHB1dCA9IFwiXCJcbmNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFJvdXRlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9ST1VURSxcbiAgICAgICAgcGFnZTogXCIvYXBpL2dlbmVyYXRlLWl0aW5lcmFyeS9yb3V0ZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL2dlbmVyYXRlLWl0aW5lcmFyeVwiLFxuICAgICAgICBmaWxlbmFtZTogXCJyb3V0ZVwiLFxuICAgICAgICBidW5kbGVQYXRoOiBcImFwcC9hcGkvZ2VuZXJhdGUtaXRpbmVyYXJ5L3JvdXRlXCJcbiAgICB9LFxuICAgIHJlc29sdmVkUGFnZVBhdGg6IFwiQzpcXFxcVXNlcnNcXFxcZGF2aWRcXFxcRG9jdW1lbnRzXFxcXEV2ZW50cmlhIEFJIFBsYW5uZXJcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcZ2VuZXJhdGUtaXRpbmVyYXJ5XFxcXHJvdXRlLnRzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgcmVxdWVzdEFzeW5jU3RvcmFnZSwgc3RhdGljR2VuZXJhdGlvbkFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIGhlYWRlckhvb2tzLCBzdGF0aWNHZW5lcmF0aW9uQmFpbG91dCB9ID0gcm91dGVNb2R1bGU7XG5jb25zdCBvcmlnaW5hbFBhdGhuYW1lID0gXCIvYXBpL2dlbmVyYXRlLWl0aW5lcmFyeS9yb3V0ZVwiO1xuZnVuY3Rpb24gcGF0Y2hGZXRjaCgpIHtcbiAgICByZXR1cm4gX3BhdGNoRmV0Y2goe1xuICAgICAgICBzZXJ2ZXJIb29rcyxcbiAgICAgICAgc3RhdGljR2VuZXJhdGlvbkFzeW5jU3RvcmFnZVxuICAgIH0pO1xufVxuZXhwb3J0IHsgcm91dGVNb2R1bGUsIHJlcXVlc3RBc3luY1N0b3JhZ2UsIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzLCBoZWFkZXJIb29rcywgc3RhdGljR2VuZXJhdGlvbkJhaWxvdXQsIG9yaWdpbmFsUGF0aG5hbWUsIHBhdGNoRmV0Y2gsICB9O1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcm91dGUuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-itinerary%2Froute&page=%2Fapi%2Fgenerate-itinerary%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-itinerary%2Froute.ts&appDir=C%3A%5CUsers%5Cdavid%5CDocuments%5CEventria%20AI%20Planner%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdavid%5CDocuments%5CEventria%20AI%20Planner&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/generate-itinerary/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/generate-itinerary/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_ai_openrouter__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/ai/openrouter */ \"(rsc)/./src/lib/ai/openrouter.ts\");\n\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        // Validate the request\n        if (!body.destinations || body.destinations.length === 0) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Destinacije su obavezne\"\n            }, {\n                status: 400\n            });\n        }\n        // Generate itinerary using OpenRouter\n        const itinerary = await _lib_ai_openrouter__WEBPACK_IMPORTED_MODULE_1__.openRouterClient.generateItinerary(body);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            itinerary\n        });\n    } catch (error) {\n        console.error(\"Error generating itinerary:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Greška pri kreiranju itinerara\",\n            details: error instanceof Error ? error.message : \"Nepoznata greška\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET() {\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n        message: \"Eventria AI Itinerary Generator\",\n        status: \"active\",\n        version: \"1.0.0\"\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9nZW5lcmF0ZS1pdGluZXJhcnkvcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUF1RDtBQUNpQjtBQUVqRSxlQUFlRSxLQUFLQyxPQUFvQjtJQUM3QyxJQUFJO1FBQ0YsTUFBTUMsT0FBeUIsTUFBTUQsUUFBUUUsSUFBSTtRQUVqRCx1QkFBdUI7UUFDdkIsSUFBSSxDQUFDRCxLQUFLRSxZQUFZLElBQUlGLEtBQUtFLFlBQVksQ0FBQ0MsTUFBTSxLQUFLLEdBQUc7WUFDeEQsT0FBT1Asa0ZBQVlBLENBQUNLLElBQUksQ0FDdEI7Z0JBQUVHLE9BQU87WUFBMEIsR0FDbkM7Z0JBQUVDLFFBQVE7WUFBSTtRQUVsQjtRQUVBLHNDQUFzQztRQUN0QyxNQUFNQyxZQUFZLE1BQU1ULGdFQUFnQkEsQ0FBQ1UsaUJBQWlCLENBQUNQO1FBRTNELE9BQU9KLGtGQUFZQSxDQUFDSyxJQUFJLENBQUM7WUFDdkJPLFNBQVM7WUFDVEY7UUFDRjtJQUNGLEVBQUUsT0FBT0YsT0FBTztRQUNkSyxRQUFRTCxLQUFLLENBQUMsK0JBQStCQTtRQUU3QyxPQUFPUixrRkFBWUEsQ0FBQ0ssSUFBSSxDQUN0QjtZQUNFRyxPQUFPO1lBQ1BNLFNBQVNOLGlCQUFpQk8sUUFBUVAsTUFBTVEsT0FBTyxHQUFHO1FBQ3BELEdBQ0E7WUFBRVAsUUFBUTtRQUFJO0lBRWxCO0FBQ0Y7QUFFTyxlQUFlUTtJQUNwQixPQUFPakIsa0ZBQVlBLENBQUNLLElBQUksQ0FBQztRQUN2QlcsU0FBUztRQUNUUCxRQUFRO1FBQ1JTLFNBQVM7SUFDWDtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXZlbnRyaWEtYWktcGxhbm5lci8uL3NyYy9hcHAvYXBpL2dlbmVyYXRlLWl0aW5lcmFyeS9yb3V0ZS50cz85OWNlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5leHRSZXF1ZXN0LCBOZXh0UmVzcG9uc2UgfSBmcm9tICduZXh0L3NlcnZlcidcbmltcG9ydCB7IG9wZW5Sb3V0ZXJDbGllbnQsIEl0aW5lcmFyeVJlcXVlc3QgfSBmcm9tICdAL2xpYi9haS9vcGVucm91dGVyJ1xuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gUE9TVChyZXF1ZXN0OiBOZXh0UmVxdWVzdCkge1xuICB0cnkge1xuICAgIGNvbnN0IGJvZHk6IEl0aW5lcmFyeVJlcXVlc3QgPSBhd2FpdCByZXF1ZXN0Lmpzb24oKVxuICAgIFxuICAgIC8vIFZhbGlkYXRlIHRoZSByZXF1ZXN0XG4gICAgaWYgKCFib2R5LmRlc3RpbmF0aW9ucyB8fCBib2R5LmRlc3RpbmF0aW9ucy5sZW5ndGggPT09IDApIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBlcnJvcjogJ0Rlc3RpbmFjaWplIHN1IG9iYXZlem5lJyB9LFxuICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgIClcbiAgICB9XG5cbiAgICAvLyBHZW5lcmF0ZSBpdGluZXJhcnkgdXNpbmcgT3BlblJvdXRlclxuICAgIGNvbnN0IGl0aW5lcmFyeSA9IGF3YWl0IG9wZW5Sb3V0ZXJDbGllbnQuZ2VuZXJhdGVJdGluZXJhcnkoYm9keSlcbiAgICBcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgIGl0aW5lcmFyeVxuICAgIH0pXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgZ2VuZXJhdGluZyBpdGluZXJhcnk6JywgZXJyb3IpXG4gICAgXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgeyBcbiAgICAgICAgZXJyb3I6ICdHcmXFoWthIHByaSBrcmVpcmFuanUgaXRpbmVyYXJhJyxcbiAgICAgICAgZGV0YWlsczogZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAnTmVwb3puYXRhIGdyZcWha2EnXG4gICAgICB9LFxuICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgKVxuICB9XG59XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBHRVQoKSB7XG4gIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgbWVzc2FnZTogJ0V2ZW50cmlhIEFJIEl0aW5lcmFyeSBHZW5lcmF0b3InLFxuICAgIHN0YXR1czogJ2FjdGl2ZScsXG4gICAgdmVyc2lvbjogJzEuMC4wJ1xuICB9KVxufVxuIl0sIm5hbWVzIjpbIk5leHRSZXNwb25zZSIsIm9wZW5Sb3V0ZXJDbGllbnQiLCJQT1NUIiwicmVxdWVzdCIsImJvZHkiLCJqc29uIiwiZGVzdGluYXRpb25zIiwibGVuZ3RoIiwiZXJyb3IiLCJzdGF0dXMiLCJpdGluZXJhcnkiLCJnZW5lcmF0ZUl0aW5lcmFyeSIsInN1Y2Nlc3MiLCJjb25zb2xlIiwiZGV0YWlscyIsIkVycm9yIiwibWVzc2FnZSIsIkdFVCIsInZlcnNpb24iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/generate-itinerary/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/ai/openrouter.ts":
/*!**********************************!*\
  !*** ./src/lib/ai/openrouter.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OpenRouterClient: () => (/* binding */ OpenRouterClient),\n/* harmony export */   openRouterClient: () => (/* binding */ openRouterClient)\n/* harmony export */ });\nconst OPENROUTER_API_KEY = \"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\";\nconst OPENROUTER_BASE_URL = \"https://openrouter.ai/api/v1\";\nclass OpenRouterClient {\n    constructor(){\n        this.apiKey = OPENROUTER_API_KEY;\n        this.baseUrl = OPENROUTER_BASE_URL;\n    }\n    async generateItinerary(request) {\n        const systemPrompt = `You are EventriaBot, an expert Croatian travel planner AI. Create detailed, personalized travel itineraries for Croatia. \n\nKey guidelines:\n- Focus on Croatian destinations, culture, and experiences\n- Include specific locations, activities, restaurants, and accommodations\n- Provide realistic timing and costs in EUR\n- Consider Croatian weather, seasons, and local events\n- Include hidden gems and local recommendations\n- Optimize routes and logistics\n- Respect the user's budget and preferences\n- Include cultural insights and local tips\n\nResponse format should be a detailed JSON itinerary with:\n- Daily schedules with specific times\n- Activity descriptions and locations\n- Cost estimates\n- Transportation details\n- Restaurant and accommodation recommendations\n- Local tips and cultural insights\n\nAlways respond in Croatian language.`;\n        const userPrompt = this.buildUserPrompt(request);\n        const messages = [\n            {\n                role: \"system\",\n                content: systemPrompt\n            },\n            {\n                role: \"user\",\n                content: userPrompt\n            }\n        ];\n        try {\n            const response = await fetch(`${this.baseUrl}/chat/completions`, {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": `Bearer ${this.apiKey}`,\n                    \"Content-Type\": \"application/json\",\n                    \"HTTP-Referer\": \"https://eventria.hr\",\n                    \"X-Title\": \"Eventria AI Travel Planner\"\n                },\n                body: JSON.stringify({\n                    model: \"anthropic/claude-3.5-sonnet\",\n                    messages,\n                    temperature: 0.7,\n                    max_tokens: 4000\n                })\n            });\n            if (!response.ok) {\n                throw new Error(`OpenRouter API error: ${response.status}`);\n            }\n            const data = await response.json();\n            return this.parseItineraryResponse(data.choices[0].message.content);\n        } catch (error) {\n            console.error(\"Error generating itinerary:\", error);\n            // Return fallback itinerary\n            return this.getFallbackItinerary(request);\n        }\n    }\n    async chatWithBot(messages, context) {\n        const systemPrompt = `You are EventriaBot, a helpful Croatian travel assistant. You help users modify and improve their travel itineraries.\n\nGuidelines:\n- Always respond in Croatian\n- Be helpful and enthusiastic about Croatian travel\n- Provide specific, actionable suggestions\n- Consider local knowledge and insider tips\n- Help with itinerary modifications, additions, and optimizations\n- Suggest alternatives when needed\n- Be concise but informative`;\n        const chatMessages = [\n            {\n                role: \"system\",\n                content: systemPrompt\n            },\n            ...messages\n        ];\n        try {\n            const response = await fetch(`${this.baseUrl}/chat/completions`, {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": `Bearer ${this.apiKey}`,\n                    \"Content-Type\": \"application/json\",\n                    \"HTTP-Referer\": \"https://eventria.hr\",\n                    \"X-Title\": \"Eventria AI Travel Planner\"\n                },\n                body: JSON.stringify({\n                    model: \"anthropic/claude-3.5-sonnet\",\n                    messages: chatMessages,\n                    temperature: 0.8,\n                    max_tokens: 1000\n                })\n            });\n            if (!response.ok) {\n                throw new Error(`OpenRouter API error: ${response.status}`);\n            }\n            const data = await response.json();\n            return data.choices[0].message.content;\n        } catch (error) {\n            console.error(\"Error in chat:\", error);\n            return \"Izvините, trenutno ne mogu odgovoriti. Molimo pokušajte ponovno.\";\n        }\n    }\n    buildUserPrompt(request) {\n        const destinations = request.destinations.join(\", \");\n        const interests = request.interests.join(\", \");\n        const budget = `${request.budget.min}-${request.budget.max} ${request.budget.currency}`;\n        let dateInfo = \"\";\n        if (request.dates.flexible) {\n            dateInfo = `Fleksibilni datumi, trajanje: ${request.dates.duration} dana`;\n        } else {\n            dateInfo = `${request.dates.startDate} do ${request.dates.endDate}`;\n        }\n        return `Kreiraj detaljni itinerar za putovanje po Hrvatskoj:\n\nDestinacije: ${destinations}\nDatumi: ${dateInfo}\nInteresi: ${interests}\nBudžet: ${budget} po osobi\nGrupa: ${request.preferences.groupSize} ${request.preferences.groupSize === 1 ? \"osoba\" : \"osoba\"}\nTip putovanja: ${request.preferences.tripType}\nTempo: ${request.preferences.travelPace}\nVremenske preferencije: ${request.preferences.weather}\n${request.preferences.accessibility ? \"Potrebna pristupačnost\" : \"\"}\n\nMolim kreiraj detaljni itinerar s aktivnostima, restoranima, smještajem i lokalnim preporukama. Uključi specifične lokacije, vremena, cijene i praktične savjete.`;\n    }\n    parseItineraryResponse(content) {\n        try {\n            // Try to extract JSON from the response\n            const jsonMatch = content.match(/\\{[\\s\\S]*\\}/);\n            if (jsonMatch) {\n                return JSON.parse(jsonMatch[0]);\n            }\n        } catch (error) {\n            console.error(\"Error parsing JSON response:\", error);\n        }\n        // If JSON parsing fails, create structured response from text\n        return this.parseTextToItinerary(content);\n    }\n    parseTextToItinerary(content) {\n        // Basic text parsing to create itinerary structure\n        const lines = content.split(\"\\n\").filter((line)=>line.trim());\n        return {\n            title: \"AI Generirani Itinerar\",\n            description: \"Personalizirani itinerar kreiran od strane EventriaBot\",\n            duration: 3,\n            totalBudget: 500,\n            currency: \"EUR\",\n            days: [\n                {\n                    day: 1,\n                    date: new Date().toISOString().split(\"T\")[0],\n                    title: \"Dan 1 - Dolazak i istraživanje\",\n                    activities: [\n                        {\n                            time: \"09:00\",\n                            title: \"Dolazak na destinaciju\",\n                            description: \"Početak vašeg putovanja\",\n                            location: \"Glavna destinacija\",\n                            duration: \"2h\",\n                            cost: 50,\n                            type: \"transport\"\n                        }\n                    ]\n                }\n            ],\n            recommendations: {\n                restaurants: [],\n                accommodations: [],\n                tips: content.split(\"\\n\").slice(0, 5)\n            }\n        };\n    }\n    getFallbackItinerary(request) {\n        const destination = request.destinations[0] || \"Zagreb\";\n        return {\n            title: `Putovanje u ${destination}`,\n            description: \"Osnovni itinerar kreiran kada AI nije dostupan\",\n            duration: request.dates.duration || 3,\n            totalBudget: request.budget.max,\n            currency: request.budget.currency,\n            days: [\n                {\n                    day: 1,\n                    date: request.dates.startDate || new Date().toISOString().split(\"T\")[0],\n                    title: \"Dan 1 - Dolazak i upoznavanje\",\n                    activities: [\n                        {\n                            time: \"10:00\",\n                            title: `Dolazak u ${destination}`,\n                            description: \"Početak vašeg putovanja\",\n                            location: destination,\n                            duration: \"2h\",\n                            cost: 50,\n                            type: \"transport\"\n                        },\n                        {\n                            time: \"14:00\",\n                            title: \"Obilazak centra grada\",\n                            description: \"Šetnja kroz glavni dio grada\",\n                            location: `Centar ${destination}`,\n                            duration: \"3h\",\n                            cost: 0,\n                            type: \"sightseeing\"\n                        }\n                    ]\n                }\n            ],\n            recommendations: {\n                restaurants: [\n                    `Lokalni restoran u ${destination}`\n                ],\n                accommodations: [\n                    `Hotel u ${destination}`\n                ],\n                tips: [\n                    \"Ponesite udobnu obuću za hodanje\",\n                    \"Provjerite radno vrijeme muzeja\",\n                    \"Pokušajte lokalnu kuhinju\"\n                ]\n            }\n        };\n    }\n}\nconst openRouterClient = new OpenRouterClient();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/ai/openrouter.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-itinerary%2Froute&page=%2Fapi%2Fgenerate-itinerary%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-itinerary%2Froute.ts&appDir=C%3A%5CUsers%5Cdavid%5CDocuments%5CEventria%20AI%20Planner%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdavid%5CDocuments%5CEventria%20AI%20Planner&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();