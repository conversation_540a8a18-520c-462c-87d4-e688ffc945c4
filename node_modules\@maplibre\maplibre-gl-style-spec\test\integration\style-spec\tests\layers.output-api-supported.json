[{"message": "layers[0]: either \"type\" or \"ref\" is required", "line": 23}, {"message": "layers[1]: missing required property \"id\"", "line": 28}, {"message": "layers[3]: \"type\" is prohibited for ref layers", "line": 42}, {"message": "layers[3]: \"source\" is prohibited for ref layers", "line": 43}, {"message": "layers[3]: \"source-layer\" is prohibited for ref layers", "line": 44}, {"message": "layers[3]: \"filter\" is prohibited for ref layers", "line": 45}, {"message": "layers[3]: \"layout\" is prohibited for ref layers", "line": 46}, {"message": "layers[4]: ref layer \"not-found\" not found", "line": 50}, {"message": "layers[5]: ref cannot reference another ref layer", "line": 54}, {"message": "layers[6]: missing required property \"source\"", "line": 56}, {"message": "layers[7]: source \"not-found\" not found", "line": 63}, {"message": "layers[8]: layer \"vector-raster-mismatch\" requires a vector source", "line": 68}, {"message": "layers[9]: layer \"raster-vector-mismatch\" requires a raster source", "line": 73}, {"message": "layers[11]: duplicate layer id \"duplicate\", previously used at line 77", "line": 83}, {"message": "layers[12].type: expected one of [fill, line, symbol, circle, heatmap, fill-extrusion, raster, hillshade, background], \"invalid\" found", "line": 90}, {"message": "layers[13]: layer \"missing-source-layer\" must specify a \"source-layer\"", "line": 100}, {"message": "layers[15]: layer \"line-gradient-bad\" specifies a line-gradient, which requires a GeoJSON source with `lineMetrics` enabled.", "line": 112}, {"message": "layers[16]: layer \"line-gradient-missing-lineMetrics\" specifies a line-gradient, which requires a GeoJSON source with `lineMetrics` enabled.", "line": 126}, {"message": "source.data: Unsupported property \"data\"", "line": 14}, {"message": "source.data: Unsupported property \"data\"", "line": 18}, {"message": "source.lineMetrics: Unsupported property \"lineMetrics\"", "line": 19}]